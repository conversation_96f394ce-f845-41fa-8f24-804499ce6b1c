import { nextTick } from 'vue';

// 表格基础配置
export const vxeBaseConfig = {
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  loading: false,
  // 始终开启纵向虚拟滚动
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 展开行配置项
  expandConfig: {
    reserve: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
    reserve: true,
  },
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    // 设置两种属性 disabled 和 editable
    beforeEditMethod({ row }: any) {
      if (row.disabled) {
        return false;
      }
      return true;
    },
  },
  menuConfig: {
    // body: {
    //   options: [
    //     [
    //       {
    //         code: 'MOVE_UP',
    //         name: '上移',
    //         prefixConfig: { icon: 'vxe-icon-arrows-up' },
    //         disabled: false,
    //       },
    //       {
    //         code: 'MOVE_DOWN',
    //         name: '下移',
    //         prefixConfig: { icon: 'vxe-icon-arrows-down' },
    //         disabled: false,
    //       },
    //       {
    //         code: 'DELETE_ROW',
    //         name: '删除',
    //         prefixConfig: { icon: 'vxe-icon-delete-fill' },
    //         disabled: false,
    //       },
    //     ],
    //   ],
    // },
    visibleMethod: ({ options, row }: any) => {
      // 内置节点控制
      if (row.disabled) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            break;
          }
          case 'MOVE_UP': {
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
};

/**
 * 将6及以上的小数 转换为 2位
 * @param cellValue - 目标值
 * @returns 两位小数金钱
 */
// export function amountFormat({ cellValue }: any) {
//   if (!cellValue) return cellValue;
//   const num = Number(cellValue);
//   // const truncated = Math.floor(num * 100) / 100;
//   return num.toFixed(2); // 返回字符串，补足两位
// }
export function amountFormat({ cellValue }: any) {
  if (!cellValue) return cellValue;
  const num = Number(cellValue);
  return num.toFixed(2); // 返回字符串，四舍五入到第两位
}

/**
 * 通过 id 重新定位表格数据
 * @param tableData - 表格的数据
 * @param tableEl - 表格的dom
 * @param currentItem - 当前的值 需要为ref
 * @param isExpand - 是否刷新后需要重新展开
 * @returns void
 */
export async function setCurrentRow(
  tableData: any,
  tableEl: any,
  currentItem: any,
  isExpand: boolean = false,
) {
  const id = currentItem.id;
  const activeRow = tableData.find((v: any) => v.id === id);
  await nextTick(() => {
    if (activeRow && tableEl) {
      tableEl.setCurrentRow(activeRow);
      currentItem = activeRow;
    }
    if (isExpand) {
      tableEl.setAllTreeExpand(isExpand);
    }
  });
}

/**
 * 重置筛选条件
 * @param columns - 表格的列
 * @param $grid - 表格的dom
 */
export function resetFilter(columns: any, $grid: any) {
  const filedMap = columns
    .filter((item: any) => !!item.filters)
    .map((v: any) => v.field);
  filedMap.forEach((field: any) => {
    $grid.clearFilter(field);
  });
  $grid.closeFilter();
}

/**
 * 查找 上一个 / 下一个 数据
 * @param columns - 表格的列
 * @param $grid - 表格的dom
 * @returns targetItem - 目前的行
 */
export function findNextOrPrevRow($grid: any, move: number) {
  const tableData = $grid.getTableData(); // 获取表格数据
  const currentRow = $grid.getCurrentRecord(); // 获取当前记录
  let rowIdx;
  // 是树形数据
  const isTree: boolean = $grid.treeConfig.transform;
  if (isTree) {
    rowIdx = $grid.getVTRowIndex(currentRow); // 用于给tree的数据上移下移
  } else {
    rowIdx = $grid.getRowIndex(currentRow); // 用于给正常的数据上移下移
  }

  let targetItem;
  // 下一个
  if (move > 0 && rowIdx < tableData.visibleData.length - 1) {
    targetItem = tableData.visibleData[rowIdx + move];
  }
  // 上一个
  if (move < 0 && rowIdx > 0) {
    targetItem = tableData.visibleData[rowIdx + move];
  }

  return targetItem;
}

/**
 * 通过 level 确认展开的层级的数据以及上级的数据 【用于展开功能】
 * @returns result 返回一个扁平的数据
 */
export function flattenTreeToLevel(tree: any[], maxLevel: number): any[] {
  const result: any[] = [];
  function traverse(nodes: any[], currentLevel: number) {
    for (const node of nodes) {
      if (currentLevel <= maxLevel) {
        result.push(node);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children, currentLevel + 1);
      }
    }
  }

  traverse(tree, 1);
  return result;
}
