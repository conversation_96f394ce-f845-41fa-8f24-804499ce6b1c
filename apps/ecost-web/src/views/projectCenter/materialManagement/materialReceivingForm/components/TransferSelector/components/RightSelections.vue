<template>
  <div class="selections h-full w-full shadow-sm">
    <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
      <div>确认材料</div>
    </div>
    <div class="h-[calc(100%-30px)] w-full">
      <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
        <template #top></template>
        <template #seq="{ $rowIndex }">
          <div>{{ $rowIndex + 1 }}</div>
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import { vxeBaseConfig } from '#/utils/vxeTool';

const props = withDefaults(
  defineProps<{
    selectionData: any;
  }>(),
  {
    selectionData: [],
  },
);

const emit = defineEmits<{
  (e: 'remove', data: any): void;
}>();

// 表格数据
const tableRef = ref();
// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'code',
    title: '单据编号',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编号',
      },
    },
  },
  {
    field: 'materialCode',
    title: '材料编码',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料编码',
      },
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料名称',
      },
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入规格型号',
      },
    },
  },
  {
    field: 'incomingUnit',
    title: '计量单位',
    width: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入计量单位',
      },
    },
  },
  {
    field: 'actualQuantity',
    title: '实收数量',
    width: '80',
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  columns,
  data: [],
});

watch(
  () => props.selectionData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    deep: true,
    immediate: true,
  },
);
// 表格事件
const gridEvents = {
  cellDblclick({ row, rowIndex }: any) {
    if (!row.disabled) {
      emit('remove', { row, rowIndex });
    }
  },
};
</script>
