<template>
  <div class="selections h-full w-full">
    <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
      <div>确认材料</div>
    </div>
    <div class="h-[calc(100%-30px)] w-full">
      <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
        <template #top></template>
        <template #seq="{ $rowIndex }">
          <div>{{ $rowIndex + 1 }}</div>
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import { vxeBaseConfig } from '#/utils/vxeTool';

const props = withDefaults(
  defineProps<{
    selectionData: any;
  }>(),
  {
    selectionData: [],
  },
);

const emit = defineEmits<{
  (e: 'remove', data: any): void;
}>();

// 表格数据
const tableRef = ref();
// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '在库材料名称',
    minWidth: '100',
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '150',
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: '80',
  },
  {
    field: 'inventoryQuantity',
    title: '库存数量',
    minWidth: '80',

    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入库存数量',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    // width: '80',
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  cellClassName: ({ column }: any) => {
    return column.editRender ? '' : 'bg-gray-100';
  },
  columns,
  data: [],
});

watch(
  () => props.selectionData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    deep: true,
    immediate: true,
  },
);
// 表格事件
const gridEvents = {
  cellDblclick({ row, rowIndex, column }: any) {
    if (!row.disabled && column.field !== 'inventoryQuantity') {
      emit('remove', { row, rowIndex });
    }
  },
  editClosed({ row }: any) {
    const $gird = tableRef.value;
    if ($gird) {
      $gird.reloadRow(row, {});
    }
  },
};
</script>
