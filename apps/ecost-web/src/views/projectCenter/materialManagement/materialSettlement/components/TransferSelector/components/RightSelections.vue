<template>
  <div class="selections h-full w-full shadow-sm">
    <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
      <div>选择单据确认</div>
    </div>
    <div class="h-[calc(100%-30px)] w-full">
      <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
        <template #top></template>
        <template #seq="{ $rowIndex }">
          <div>{{ $rowIndex + 1 }}</div>
        </template>
        <template #purchaseType="{ row }">
          <div>
            {{ getPurchaseTypeLabel(row.purchaseType) }}
          </div>
        </template>
        <template #submitStatus="{ row }">
          <div
            :class="{
              'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
              'text-red-500': row.submitStatus === SubmitStatus.PENDING,
            }"
          >
            {{ getSubmitStatusLabel(row.submitStatus) }}
          </div>
        </template>
        <template #auditStatus="{ row }">
          <div
            :class="{
              'text-red-500': row.auditStatus === AuditStatus.PENDING,
              'text-orange-500':
                row.auditStatus === AuditStatus.AUDITING ||
                row.auditStatus === AuditStatus.REJECTED,
              'text-green-500': row.auditStatus === AuditStatus.APPROVED,
            }"
          >
            {{ getAuditStatusLabel(row.auditStatus) }}
          </div>
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getPurchaseTypeLabel,
  getSubmitStatusLabel,
  purchaseTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import { amountFormat, vxeBaseConfig } from '#/utils/vxeTool';

const props = withDefaults(
  defineProps<{
    selectionData: any;
  }>(),
  {
    selectionData: [],
  },
);

const emit = defineEmits<{
  (e: 'remove', data: any): void;
}>();

// 表格数据
const tableRef = ref();
// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'code',
    title: '单据编号',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编号',
      },
    },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    minWidth: '100',
    slots: {
      default: 'purchaseType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: purchaseTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择采购类型',
      },
    },
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料类别',
      },
    },
  },
  {
    field: 'taxExcludedAmount',
    title: '金额',
    minWidth: '100',

    formatter: amountFormat,
  },
  {
    field: 'creator',
    title: '编制人',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编制人',
      },
    },
  },
  {
    field: 'billDate',
    title: '收料日期',
    width: '100',
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '100',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审核状态',
    width: '100',
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  columns,
  data: [],
});

watch(
  () => props.selectionData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    deep: true,
    immediate: true,
  },
);
// 表格事件
const gridEvents = {
  cellDblclick({ row, rowIndex }: any) {
    if (!row.disabled) {
      emit('remove', { row, rowIndex });
    }
  },
};
</script>
