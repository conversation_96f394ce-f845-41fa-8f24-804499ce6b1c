<template>
  <Page class="ml-4 mt-4 h-full rounded bg-white">
    <div class="flex h-full">
      <!-- 左侧统计金额、合同数、材料列表 -->
      <div class="area-left h-full pr-2">
        <div class="flex items-center">
          <span>选择统计账期</span>
          <ElSelect
            class="ml-1"
            style="width: 120px"
            v-model="period"
            placeholder="请选择"
            @change="periodSelect"
            clearable
          >
            <ElOption
              v-for="valueList in periodList"
              :key="valueList.value"
              :label="valueList.label"
              :value="valueList.value"
            />
          </ElSelect>
        </div>
      </div>
      <!-- 右侧列表 -->
      <div class="area-right h-full flex-1 overflow-auto">
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <!-- 新增 重置 导出相关操作 -->
          <template #top>
            <div class="flex h-[48px] items-center justify-between gap-4">
              <div class="mb-2 flex items-center gap-2">
                <ElButton
                  type="primary"
                  size="small"
                  @click="debounceAddPurchaseData"
                  v-auth="actionPermissions.apCreate"
                >
                  新增采购结算
                </ElButton>
                <ElButton
                  type="primary"
                  size="small"
                  v-auth="actionPermissions.apCreate"
                >
                  新增商砼结算
                </ElButton>
                <ElButton
                  type="primary"
                  size="small"
                  v-auth="actionPermissions.apCreate"
                >
                  新增租赁结算
                </ElButton>
                <ElButton
                  type="primary"
                  size="small"
                  v-auth="actionPermissions.apCreate"
                >
                  新增调拨结算
                </ElButton>
                <ElButton type="default" size="small" @click="filter">
                  重置筛选
                </ElButton>
              </div>
              <div class="flex items-center gap-4">
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出单据
                  </ElButton>
                </div>
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出列表
                  </ElButton>
                </div>
              </div>
            </div>
          </template>

          <!-- 行号 -->
          <template #seq="{ $rowIndex }">
            <div>{{ $rowIndex + 1 }}</div>
          </template>

          <!-- 单据编码 -->
          <template #code="{ row }">
            <div>
              <ElButton
                size="small"
                type="primary"
                link
                @click="openDetail(row)"
              >
                {{ row.code }}
              </ElButton>
            </div>
          </template>

          <!-- 结算类型 -->
          <template #settlementType="{ row }">
            <div>
              {{ getSettlementTypeLabel(row.settlementType) }}
            </div>
          </template>

          <!-- 结算日期 -->
          <template #settlementDate="{ row }">
            <div>
              {{ dayjs(`${row.settlementDate}`).format('YYYY-MM-DD') }}
            </div>
          </template>

          <template #submitStatus="{ row }">
            <div
              :class="{
                'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                'text-red-500': row.submitStatus === SubmitStatus.PENDING,
              }"
            >
              {{ getSubmitStatusLabel(row.submitStatus) }}
            </div>
          </template>
          <template #auditStatus="{ row }">
            <div
              :class="{
                'text-red-500': row.auditStatus === AuditStatus.PENDING,
                'text-orange-500':
                  row.auditStatus === AuditStatus.AUDITING ||
                  row.auditStatus === AuditStatus.REJECTED,
                'text-green-500': row.auditStatus === AuditStatus.APPROVED,
              }"
            >
              {{ getAuditStatusLabel(row.auditStatus) }}
            </div>
          </template>
          <template #qrcode="{ row }">
            <ElPopover
              placement="left"
              trigger="click"
              v-if="row.submitStatus === SubmitStatus.SUBMITTED"
            >
              <div>
                <QrcodeVue :value="row.code" :size="120" />
              </div>
              <template #reference>
                <div class="flex justify-center">
                  <QrcodeVue :value="row.code" :size="28" />
                </div>
              </template>
            </ElPopover>
          </template>
        </VxeGrid>
      </div>
    </div>

    <EditDrawer
      v-model:visible="drawerVisible"
      :info-data="infoData"
      :editable="editable"
      @move="contractSelectMove"
      @refresh="refreshData"
    />
  </Page>
</template>

<script lang="ts" setup>
import type { getMaterialSettlementListType } from '#/api/projectCenter/materialManagement/materialSettlement';

import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import {
  ElButton,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElPopover,
  ElSelect,
} from 'element-plus';
import _ from 'lodash';
import QrcodeVue from 'qrcode.vue';

import {
  addMaterialSettlement,
  changeAuditStatus,
  delInspectionBill,
  getMaterialSettlementList,
  getMaterialSettlementPeriodList,
} from '#/api/projectCenter/materialManagement/materialSettlement';
import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getSettlementTypeLabel,
  getSubmitStatusLabel,
  settlementTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import {
  findNextOrPrevRow,
  resetFilter,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

import EditDrawer from './components/EditDrawer.vue';

const { actionPermissions } = getCurrentPremission();
const debounceAddPurchaseData = _.debounce(addData.bind(null, 'PURCHASE'), 500);
const debounceAddAllocationFromData = _.debounce(
  addData.bind(null, 'ALLOCATION_FROM'),
  500,
);
const debounceAddConcreteData = _.debounce(addData.bind(null, 'CONCRETE'), 500);
const debounceAddRentalTurnoverData = _.debounce(
  addData.bind(null, 'RENTAL_TURNOVER'),
  500,
);

// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'code',
    title: '单据编码',
    width: '150',
    slots: {
      default: 'code',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'settlementType',
    title: '结算类型',
    minWidth: '150',
    slots: {
      default: 'settlementType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: settlementTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择结算类型',
      },
    },
  },
  {
    field: 'supplierName',
    title: '供应商名称',
    width: '150',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'contractName',
    title: '合同名称',
    width: '200',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'priceType',
    title: '价格类型',
    width: '200',
  },
  {
    field: 'taxExcludedAmount',
    title: '本期结算金额(不含税)',
    width: '90',
  },
  {
    field: 'taxIncludedAmount',
    title: '本期结算金额(含税)',
    width: '90',
  },
  {
    field: 'creator',
    title: '编制人',
    width: '150',
  },
  {
    field: 'settlementDate',
    title: '结算日期',
    width: '150',
    slots: {
      default: 'settlementDate',
    },
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '150',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审批状态',
    width: '150',
    editRender: {
      name: 'VxeSelect',
      options: auditStatusOption,
    },
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
  {
    title: '二维码',
    width: '150',
    slots: {
      default: 'qrcode',
    },
  },
];

// 侧抽屉数据
const drawerVisible = ref(false);
const infoData = ref();
const editable = ref(true); // 是否可编辑

// 当前点击项
const currentItem = ref<any>();
const period = ref();
const periodList = ref<any>([]);

const tableRef = ref();

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ row, options }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled =
              !actionPermissions.apDelete ||
              row.submitStatus !== SubmitStatus.PENDING;
            break;
          }

          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前无修改权限');
        return;
      }
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交数据');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;

    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionBill(id);
        if (res) {
          await refreshData();
          ElMessage.success('删除成功');
        }
      });
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (column.field === 'auditStatus') {
      const id = row.id;
      const item = auditStatusOption.find(
        (item) => item.value === row.auditStatus,
      );

      ElMessageBox.confirm(`你确定要${item?.label}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await changeAuditStatus(id, row.auditStatus);
          if (res) {
            refreshData();
            ElMessage.success(`${item?.label}成功`);
          }
        })
        .catch(async () => {
          refreshData();
        });
    }
  },
};

async function filter() {
  const $grid = tableRef.value;
  resetFilter(tableOptions.columns, $grid);
}

// 新增数据
async function addData(settlementType: string) {
  const res = await addMaterialSettlement({ settlementType });
  res.orgName = res.projectName;
  infoData.value = res;
  currentItem.value = res;
  await refreshData();
  drawerVisible.value = true;
}

// 打开详情
async function openDetail(row: any, isOpen = true) {
  row.orgName = row.projectName;
  infoData.value = row;
  if (isOpen) {
    drawerVisible.value = true;
  }
}

async function getList() {
  tableOptions.loading = true;

  const params: getMaterialSettlementListType = {};

  if (period.value) {
    const [year, month] = period.value.split('-');
    params.year = year;
    params.month = month;
    params.isCumulation = false;
  } else {
    params.isCumulation = true;
  }

  const res = await getMaterialSettlementList(params);

  tableOptions.data = res;
  tableOptions.loading = false;
}

// 账期选择
async function periodSelect(value: any) {
  period.value = value;
  await getList();
}

async function getPeriodList() {
  const res = await getMaterialSettlementPeriodList();
  res.forEach((item: any) => {
    periodList.value = [
      {
        label: `${item.year}年${item.month}月`,
        value: `${item.year}-${item.month}`,
      },
    ];
  });
}

// 合同选中移动
async function contractSelectMove(move: number) {
  const $grid = tableRef.value;
  const targetItem = findNextOrPrevRow($grid, move);
  if (targetItem) {
    tableEvents.cellClick({ row: targetItem });
    setCurrentRow(tableOptions.data, tableRef.value, targetItem);
    openDetail(targetItem, false);
  }
}

// 刷新
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
}

// 初始化
async function init() {
  await getPeriodList();
  await getList();
}

// 渲染前
onMounted(async () => {
  await init();

  // openRoutePage();
});
</script>

<style lang="scss">
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
