<template>
  <ElDialog
    v-bind="$attrs"
    v-model="dialogVisible"
    :destroy-on-close="true"
    :width="600"
    :title="title"
    :foot="false"
    @close="dialogClosed"
    @open="onOpen"
  >
    <ElForm
      :model="localFormData"
      ref="formEl"
      :rules="formRules"
      label-width="160px"
    >
      <div v-if="!isPatchContract">
        <ElFormItem label="合同名称" prop="name">
          <ElInput
            v-model="localFormData.name"
            :disabled="!localEditable"
            placeholder="请输入合同名称"
          />
        </ElFormItem>
        <ElFormItem label="合同编码" prop="code">
          <ElInput
            v-model="localFormData.code"
            :disabled="!localEditable"
            placeholder="请输入合同编码"
          />
        </ElFormItem>
        <ElFormItem label="选择甲方" prop="partyA">
          <ElSelect
            v-model="localFormData.partyA"
            :disabled="!localEditable || isEditStatus"
            filterable
          >
            <ElOption
              v-for="v in projectAOption"
              :key="v.id"
              :value="v.id"
              :label="v.name"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="选择乙方" prop="partyB">
          <ElSelect
            v-model="localFormData.partyB"
            @change="partyBSelectChange"
            :disabled="!localEditable || isEditStatus"
            filterable
          >
            <ElOption
              v-for="v in projectBOption"
              :key="v.id"
              :value="v.id"
              :label="v.name"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="选择合同范本" prop="contractTemplateId">
          <ElSelect
            v-model="localFormData.contractTemplateId"
            :disabled="!localEditable || isEditStatus"
            filterable
          >
            <ElOption
              v-for="v in contractTemplateOption"
              :key="v.id"
              :value="v.id"
              :label="v.name"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="拟定状态" prop="proposedStatus">
          <ElSelect
            v-model="localFormData.proposedStatus"
            :disabled="
              !localEditable ||
              props.formData.proposedStatus === ProposedStatus.OFFICIAL
            "
            filterable
          >
            <ElOption :value="ProposedStatus.OFFICIAL" label="正式合同" />
            <ElOption :value="ProposedStatus.PROVISIONAL" label="暂定合同" />
          </ElSelect>
        </ElFormItem>
      </div>

      <div v-else>
        <ElFormItem label="补充协议名称" prop="name">
          <ElInput
            v-model="localFormData.name"
            :disabled="!patchlocalEditable"
            placeholder="请输入合同名称"
          />
        </ElFormItem>
        <ElFormItem label="补充协议编码" prop="code">
          <ElInput
            v-model="localFormData.code"
            :disabled="!patchlocalEditable"
            placeholder="请输入合同编码"
          />
        </ElFormItem>
        <ElFormItem label="补充协议范本" prop="contractTemplateId">
          <ElSelect
            v-model="localFormData.contractTemplateId"
            :disabled="!patchlocalEditable"
          >
            <ElOption
              :value="localFormData.contractTemplateId"
              label="空白范本(系统内置)"
            />
          </ElSelect>
        </ElFormItem>
      </div>
    </ElForm>
    <template #footer>
      <div v-loading="contentLoading">
        <ElButton @click="dialogClosed">取消</ElButton>
        <ElButton type="primary" @click="submit">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { computed, ref, watch } from 'vue';

import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElSelect,
} from 'element-plus';
import cloneDeep from 'lodash/cloneDeep';

import {
  addContractCompilation,
  editContractCompilation,
  getContractTemplateList,
  getPartyACompanyList,
  getPartyBCompanyList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}

const props = withDefaults(
  defineProps<{
    formData: addOrEditFormType;
    infoData: any;
    visible: boolean;
  }>(),
  {
    visible: false,
    formData: () => ({
      id: null,
      parentId: null,

      name: '', // 合同名称
      code: '', // 合同编号
      partyA: '', // 甲方id
      partyB: '', // 乙方id
      partyBType: '', // 乙方公司类型
      contractTemplateId: '', // 合同范本id
      proposedStatus: '', // 拟定状态
    }),
    infoData: {
      parentId: null,
      contractId: '',
      contractTemplateType: '',
      auditStatus: '',
      submitStatus: '',
    },
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();
const SubmitStatus = {
  PENDING: 'PENDING', // 未提交
  SUBMITTED: 'SUBMITTED', // 已提交
} as const;

// 审核状态
const AuditStatus = {
  PENDING: 'PENDING', // 未审批
  AUDITING: 'AUDITING', // 审批中
  APPROVED: 'APPROVED', // 已审批
  REJECTED: 'REJECTED', // 被退回
} as const;

// 拟定状态
const ProposedStatus = {
  OFFICIAL: 'OFFICIAL', // 正式合同
  PROVISIONAL: 'PROVISIONAL', // 暂定合同
} as const;
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);

// 合同信息数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
  },
);
// 是否可编辑的状态
const localEditable = computed(() => {
  return localInfoData.value.submitStatus === SubmitStatus.PENDING;
});

// 是否可编辑的状态
const patchlocalEditable = computed(() => {
  const isEdit = !!localInfoData.value.parentId;

  return isEdit
    ? localInfoData.value.submitStatus === SubmitStatus.PENDING
    : localInfoData.value.submitStatus === SubmitStatus.SUBMITTED &&
        localInfoData.value.auditStatus === AuditStatus.APPROVED;
});

const isPatchContract = computed(() => {
  return !!localFormData.value.parentId;
});

const isEditStatus = computed(() => {
  return !!localFormData.value.id;
});

const title = computed(() => {
  if (localFormData.value.parentId) {
    return '新增/编辑补充协议';
  } else {
    return localFormData.value.id ? '编辑合同信息' : '新增合同';
  }
});

const localFormData = ref<addOrEditFormType>(props.formData);
const contractTemplateId = ref();
watch(
  () => props.formData,
  (nval) => {
    localFormData.value = cloneDeep(nval);
    contractTemplateId.value = localFormData.value.contractTemplateId;
  },
  { deep: true, immediate: true },
);
// 表单数据
const formEl = ref();
const formRules = ref({
  name: [
    {
      required: true,
      message: localFormData.value.parentId
        ? '请输入协议名称'
        : `请输入合同名称`,
      trigger: ['blur', 'change'],
    },
  ],
  code: [
    {
      required: true,
      message: localFormData.value.parentId
        ? '请输入协议编码'
        : `请输入合同编码`,
      trigger: ['blur', 'change'],
    },
  ],
  partyA: [
    {
      required: true,
      message: `请选择甲方`,
    },
  ],
  partyB: [
    {
      required: true,
      message: `请选择乙方`,
    },
  ],
  contractTemplateId: [
    {
      required: true,
      message: localFormData.value.parentId
        ? '请选择补充协议范本'
        : `请选择合同范本`,
    },
  ],
  proposedStatus: [
    {
      required: true,
      message: `请选择拟定状态`,
    },
  ],
});
const projectAOption = ref<any>([]); // 甲方公司数据
const projectBOption = ref<any>([]); // 乙方公司数据
const contractTemplateOption = ref<any>([]); // 合同范本数据
function partyBSelectChange(val: string) {
  const option = projectBOption.value.find((v: any) => v.id === val);
  localFormData.value.partyBType = option.type;
}
// 关闭弹窗
function dialogClosed() {
  emit('update:visible', false);
}
const contentLoading = ref(false);
// 提交
const submit = async () => {
  formEl.value.validate(async (valid: boolean) => {
    const { id, parentId, ...params } = localFormData.value;
    if (valid) {
      // 补充协议
      contentLoading.value = true;
      if (localFormData.value.parentId) {
        if (id) {
          const res = await editContractCompilation(id, {
            parentId,
            name: params.name,
            code: params.code,
            contractTemplateId: params.contractTemplateId,
          }).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('修改成功');

            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        } else {
          const res = await addContractCompilation({
            parentId,
            ...params,
          }).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('新增成功');
            contentLoading.value = false;
            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        }
      } else {
        // 合同
        if (id) {
          const res = await editContractCompilation(id, params).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('修改成功');
            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        } else {
          const res = await addContractCompilation(params).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('新增成功');
            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        }
      }
    }
  });
};
// 初始化
async function init() {
  projectAOption.value = await getPartyACompanyList({});
  projectBOption.value = await getPartyBCompanyList({});
  contractTemplateOption.value = await getContractTemplateList({});
}

// 新增合同打开后初始化
async function onOpen() {
  if (!isPatchContract.value) {
    init();
  }
}
</script>
<style scoped lang="scss"></style>
