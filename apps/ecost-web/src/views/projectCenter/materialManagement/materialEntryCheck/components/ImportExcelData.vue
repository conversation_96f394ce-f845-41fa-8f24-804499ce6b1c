<template>
  <ElDialog
    v-model="visible"
    title="导入问题数据"
    width="80%"
    @opened="handleOpenedDialog"
    :before-close="handleCloseDialog"
    :z-index="998"
  >
    <div>
      <VxeGrid v-bind="tableOptions" @current-row-change="handleRowChange" />
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div>
          <span style="mr-10">
            导入成功行数={{ props.successCount }}，导入失败行数计数={{
              props.errorCount
            }}
            ，合计={{ props.totalCount }}
          </span>
        </div>
        <div>
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" @click="handleConfirm"> 确定 </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<script lang="ts" setup>
import type { PurchaseTypeEnum } from '#/types/materialManagement';

import { emit } from 'node:process';

import { computed, defineProps, reactive, ref } from 'vue';

import { ElButton, ElDialog, ElMessageBox } from 'element-plus';

import { getSupplierAndContractList } from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import {
  getPurchaseTypeLabel,
  purchaseTypeLabelOption,
} from '#/types/materialManagement';
import { vxeBaseConfig } from '#/utils/vxeTool';

const props = defineProps({
  modelValue: Boolean,
  records: {
    type: Array,
    default: () => [],
  },
  totalCount: {
    type: Number,
    default: 0,
  },
  errorCount: {
    type: Number,
    default: 0,
  },
  successCount: {
    type: Number,
    default: 0,
  },
});

// 使用计算属性来处理 v-model
const emit = defineEmits(['update:modelValue']);
const supplierNameMap: Record<string, string> = {};
const contractNameMap: Record<string, string> = {};
// 弹窗打开回调
function handleOpenedDialog() {
  tableOptions.data = props.records;

  // 记录供应商和合同id=>name的映射
  for (const item of props.records as any) {
    if (item.supplierId) {
      supplierNameMap[item.supplierId] = item.supplierName;
    }
    if (item.contractId) {
      contractNameMap[item.contractId] = item.contractName;
    }
  }
}

function getSupplierName(supplierId: string) {
  return supplierNameMap[supplierId] || supplierId;
}

function getContractName(contractId: string) {
  return contractNameMap[contractId] || contractId;
}

const visible = computed({
  get: () => props.modelValue,
  set: () => emit('update:modelValue', false),
});

// 处理取消按钮
const handleCancel = () => {
  ElMessageBox.confirm('弹窗关闭后，数据不会保存！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    visible.value = false;
  });
};
// 弹窗关闭回调
const handleCloseDialog = (done: () => void) => {
  ElMessageBox.confirm('弹窗关闭后，数据不会保存！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    visible.value = false;
    done();
  });
};

// 处理确定按钮
const handleConfirm = () => {};

// 供应商名录下拉数据
const supplierOptions = ref([]);
// 合同下拉数据
const contractOptions = ref([]);
// 材料下拉数据
const materialOptions = ref([]);
// 计量单位下拉数据
const unitOptions = ref([]);

async function handleRowChange({ row }: { row: any }) {
  if (row.purchaseType?.validMsg) return;

  const purchaseType = row.purchaseType;
  const supplierAndContracts: any = await getSupplierAndContracts(purchaseType);

  // 刷新供应商名录下拉数据
  supplierOptions.value = [];
  supplierOptions.value = supplierAndContracts.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
}

// 获取对应采购类型的下拉数据
const purchaseTypeSelectionDataMap: Partial<Record<PurchaseTypeEnum, any>> = {};
async function getSupplierAndContracts(purchaseType: PurchaseTypeEnum) {
  if (!purchaseTypeSelectionDataMap[purchaseType]) {
    // 获取供应商和合同数据
    purchaseTypeSelectionDataMap[purchaseType] =
      await getSupplierAndContractList({
        purchaseType,
      });
  }

  return purchaseTypeSelectionDataMap[purchaseType];
}

// 表格配置
const columns = [
  {
    field: 'rowNo',
    title: '行数',
    width: 50,
  },
  {
    field: 'siteEntryDate',
    title: '进场日期',
    width: 100,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue.text || cellValue;
    },
    editRender: { name: 'VxeDatePicker', props: { type: 'date' } },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    width: 80,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue.text || getPurchaseTypeLabel(cellValue);
    },
    editRender: {
      name: 'VxeSelect',
      options: purchaseTypeLabelOption,
      events: {
        change: (data) => {
          console.log(data);
        },
      },
    },
  },
  {
    field: 'supplierName',
    title: '供应商名称',
    width: 150,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue.text || getSupplierName(cellValue);
    },
    editRender: {
      name: 'VxeSelect',
      options: supplierOptions,
      events: {
        change: (data) => {
          console.log(data);
        },
      },
    },
  },
  {
    field: 'contractName',
    title: '合同名称',
    width: 150,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue.text || getContractName(cellValue);
    },
  },
  {
    field: 'materialCode',
    title: '材料编码',
    width: 100,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue.text || cellValue;
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    width: 100,
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    width: 100,
  },
  {
    field: 'qualityStandard',
    title: '质量标准',
    width: 150,
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入质量标准',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: 80,
  },
  {
    field: 'siteEntryQuantity',
    title: '进场数量',
    width: 100,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue.text || cellValue;
    },
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入实收数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
  {
    field: 'actualQuantity',
    title: '实收数量',
    width: 100,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue.text || cellValue;
    },
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入实收数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
  {
    field: 'appearanceDescription',
    title: '外观质量描述',
    width: 150,
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入外观质量描述',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入备注',
      },
    },
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  treeConfig: null,
  height: '100%',
  loading: false,
  columns,
  data: [],
  rowClassName: () => {},
  cellClassName({ row, column }: any) {
    const { field } = column;
    if (row[field]?.validMsg) {
      return 'text-red-500';
    }
  },
  tooltipConfig: {
    enterable: true,
    contentMethod: ({ column, row }: any) => {
      const { field } = column;
      if (row[field]?.validMsg) {
        return row[field]?.validMsg;
      }
    },
  },
});
</script>
