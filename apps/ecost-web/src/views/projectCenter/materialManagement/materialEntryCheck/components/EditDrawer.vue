<template>
  <div class="edit-drawer relative h-[100%]">
    <ElDrawer
      v-bind="$attrs"
      v-model="drawerVisible"
      :modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :size="sizeNum"
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      :with-header="false"
      @close="handleClose"
      @opened="handleOpen"
    >
      <div class="h-full w-full">
        <div class="header box-border w-full pl-4 pr-4">
          <div class="flex h-[60px] w-full items-center justify-between">
            <div class="header-left flex items-center justify-between"></div>
            <div class="header-right flex">
              <div class="btn-group ml-4 pr-4">
                <ElButton type="primary" size="default" @click="insureSubmit">
                  {{
                    localInfoData.submitStatus === SubmitStatus.PENDING
                      ? '提交'
                      : '取消提交'
                  }}
                </ElButton>
                <!-- <ElButton type="default" size="default" disabled>
                导出单据
              </ElButton> -->
              </div>
              <div class="flex">
                <IconifyIcon
                  @click="prevBtn"
                  class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-left-line"
                />
                <IconifyIcon
                  @click="nextBtn"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-right-line"
                />
              </div>
              <div>
                <ElTooltip
                  :content="isFullScreen ? '收起' : '全屏'"
                  placement="bottom"
                >
                  <IconifyIcon
                    @click="isFullScreen = !isFullScreen"
                    class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                    :icon="
                      isFullScreen
                        ? 'majesticons:arrows-collapse-full'
                        : 'majesticons:arrows-expand-full-line'
                    "
                  />
                </ElTooltip>
              </div>
              <div>
                <IconifyIcon
                  @click="closeClick"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="ep:close"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content h-[calc(100%-60px)] p-[24px] pb-0 pt-2">
          <div class="relative flex h-[68px] items-center justify-center">
            <div class="title flex text-[24px] font-bold">
              物资进场验收记录 (
              <div
                :class="{
                  'text-red-500':
                    localInfoData.submitStatus === SubmitStatus.PENDING,
                  'text-orange-500':
                    localInfoData.auditStatus === AuditStatus.AUDITING ||
                    localInfoData.auditStatus === AuditStatus.REJECTED,
                  'text-green-500':
                    localInfoData.auditStatus === AuditStatus.APPROVED ||
                    localInfoData.submitStatus === SubmitStatus.SUBMITTED,
                }"
              >
                <!-- 如果是 待审核 ： 提交状态 ， 如果不是 ： 审核状态 -->
                {{
                  localInfoData.auditStatus === AuditStatus.PENDING
                    ? getSubmitStatusLabel(localInfoData.submitStatus)
                    : getAuditStatusLabel(localInfoData.auditStatus)
                }}
              </div>
              )
            </div>
            <div
              class="qrcode absolute right-20 flex items-center"
              v-if="localInfoData.submitStatus === SubmitStatus.SUBMITTED"
            >
              <QrcodeVue value="物资进场验收记录" :size="80" />
              <!-- <div class="ml-4 text-sm">导出{{ 2 }}次</div> -->
            </div>
          </div>

          <div class="content flex h-[calc(100%-114px)] w-full flex-col">
            <ElForm
              :model="formData"
              class="grid grid-cols-3 gap-x-20 gap-y-1 pb-1 pt-6"
            >
              <ElFormItem
                label="项目名称："
                label-width="106px"
                size="large"
                label-position="left"
              >
                <div>{{ formData.orgName }}</div>
              </ElFormItem>
              <ElFormItem
                label="采购类型："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElSelect
                  size="large"
                  v-model="formData.purchaseType"
                  placeholder=""
                  filterable
                  @change="purchaseypeChange"
                  :disabled="!localEditable"
                >
                  <ElOption
                    v-for="v in purchaseTypeLabelOption"
                    :key="v.value"
                    :label="v.label"
                    :value="v.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="单据编码："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElInput
                  size="large"
                  v-model="formData.code"
                  placeholder=""
                  disabled
                />
              </ElFormItem>
              <ElFormItem
                label="供应商名称："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.supplierName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.supplierId"
                      placeholder=""
                      @change="supplierChange"
                      clearable
                      filterable
                      :disabled="!localEditable"
                    >
                      <ElOption
                        v-for="v in supplierOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="合同名称："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.contractName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.contractId"
                      placeholder=""
                      clearable
                      filterable
                      :disabled="
                        formData.purchaseType ===
                          PurchaseType.PARTY_A_SUPPLIED ||
                        formData.purchaseType === PurchaseType.TRANSFER_IN ||
                        formData.supplierName === '零星材料供应商' ||
                        !localEditable
                      "
                      @change="contractChange"
                    >
                      <ElOption
                        v-for="v in contractOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="进场时间："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElDatePicker
                  size="large"
                  v-model="formData.entryDate"
                  type="date"
                  placeholder=""
                  :clearable="false"
                  disabled
                />
              </ElFormItem>
            </ElForm>

            <div class="flex-1 overflow-auto">
              <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
                <template #seq="{ row }">
                  <div v-if="row.id">{{ row.index }}</div>
                </template>
                <template #materialName="{ row, $rowIndex }">
                  <div class="flex items-center justify-center">
                    <div class="flex">
                      <div>{{ row.materialName }}</div>
                      <div
                        v-if="
                          ($rowIndex === 1 && tableOptions.data.length <= 0) ||
                          $rowIndex === tableOptions.data.length - 1
                        "
                      >
                        <ElButton
                          size="small"
                          @click="transferDataClick"
                          :disabled="!localEditable"
                        >
                          +
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </template>
              </VxeGrid>
            </div>

            <ElForm class="info grid grid-cols-3 gap-x-20 pt-2">
              <ElFormItem
                label="材料员："
                label-width="68px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="施工员："
                label-width="68px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="分包材料员："
                label-width="106px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="编制人："
                label-width="68px"
                size="large"
                label-position="left"
              >
                {{ formInfo.creator }}
              </ElFormItem>
            </ElForm>
          </div>

          <div class="footer flex items-center">
            <!-- <ElButton type="primary" size="default" @click="insureAduit">
            发起审核
          </ElButton> -->
          </div>
        </div>

        <ExtrasPanel
          v-model:file-list="fileList"
          :visible-option="['ANNEX']"
          :editable="localEditable"
          list-type="picture"
          @del-annex="removeAnnex"
          @success="addAnnex"
        />
      </div>

      <AddOrEditMaterial
        v-model:visible="addOrEditMaterialVisible"
        :info-data="addMaterialinfoData"
        title="选择材料"
        @refresh="refreshData"
      />
    </ElDrawer>
  </div>
</template>

<script lang="ts" setup>
import type { PurchaseTypeEnum } from '#/types/materialManagement';

import { computed, nextTick, provide, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  dayjs,
  ElButton,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addInspectionBillAttachment,
  delAttachmen,
  delInspectionDetail,
  editInspectionBill,
  editInspectionDetail,
  getAttachmentList,
  getInspectionDetailList,
  getSupplierAndContractList,
  moveInspectionDetail,
} from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import ExtrasPanel from '#/components/ExtrasPanel/index.vue';
import {
  AuditStatus,
  getAuditStatusLabel,
  getSubmitStatusLabel,
  PurchaseType,
  purchaseTypeLabelOption,
  SubmitStatus,
} from '#/types/materialManagement';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

import AddOrEditMaterial from './AddOrEditMaterial.vue';

defineOptions({
  name: 'MaterialEntryCheckEditDrawer',
});
const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {},
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();

// 附件数据
const fileList = ref([]);

// 供应商和合同名称的总数据
const contractAndSupplierOptions = ref<any>([]);
// 供应商选项
const supplierOptions = ref<any>([]);
// 合同选项
const contractOptions = ref<any>([]);

// 是否展示弹窗
const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
const curTab = ref();
provide('curTab', curTab);
// 表单数据
const formData = ref({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});
const oldFormData = ref({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});
// 每次表单数据改变的时候就调用修改接口

// 底部展示数据
const formInfo = ref({
  user: '',
  creator: '',
});
// 全部的外层数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval) => {
    localInfoData.value = nval;
    await getList();

    await getAnnexlist();

    const {
      id,
      orgName,
      purchaseType,
      code,
      supplierName,
      contractName,
      year,
      day,
      month,
      supplierId,
      contractId,
      creator,
    } = nval;
    const form = {
      id,
      orgName,
      supplierId,
      contractId,
      purchaseType, // 默认为自采
      code,
      supplierName,
      contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
    };
    formData.value = form;
    oldFormData.value = structuredClone(form);
    formInfo.value = {
      user: '',
      creator,
    };

    // 调用获取供应商名称
    await getSupplierContractlList();

    // 根据供应商Id 给 合同名称Id 赋值

    if (supplierId) {
      const data = contractAndSupplierOptions.value.find(
        (item: any) => item.id === formData.value.supplierId,
      );

      contractOptions.value = data?.contracts
        ? data.contracts.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          })
        : [];
    } else {
      contractOptions.value = [];
    }
  },
);

const localEditable = computed(() => {
  return localInfoData.value.submitStatus === SubmitStatus.PENDING;
});

watch(
  () => formData,
  (nval, oval) => {
    oldFormData.value = oval;
  },
);

// 采购类型改变
const purchaseypeChange = async () => {
  if (tableOptions.data.length > 1) {
    // 还原为旧值
    nextTick(() => {
      formData.value.purchaseType = oldFormData.value.purchaseType;
    });

    ElMessage.warning('请先删除材料数据后再修改');
  } else {
    formData.value.supplierId = '';
    formData.value.supplierName = '';

    formData.value.contractId = '';
    formData.value.contractName = '';

    supplierOptions.value = [];
    contractOptions.value = [];

    await getSupplierContractlList();

    if (supplierOptions.value.length === 1) {
      formData.value.supplierId = supplierOptions.value[0].value;
      formData.value.supplierName = supplierOptions.value[0].label;
    } else {
      formData.value.supplierId = '';
      formData.value.supplierName = '';
    }

    await insureSave();

    await getList();
  }
};
// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '64%';
});

const addOrEditMaterialVisible = ref(false);

const addMaterialinfoData = ref();

// 表格配置
const emptyGoodsItem = {
  id: '',
  name: '',
  editable: false,
  disabled: false,
  internal: true,
};

const tableRef = ref();
const currentItem = ref();
const unitOptions = ref([]);
const columns = [
  {
    type: 'seq',
    field: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '140',
    slots: {
      default: 'materialName',
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '100',
  },
  {
    field: 'qualityStandard',
    title: '质量标准',
    minWidth: '100',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入质量标准',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: '100',
    editRender: {
      name: 'VxeSelect',
      options: unitOptions,
    },
  },
  {
    field: 'siteEntryQuantity',
    title: '进场数量',
    minWidth: '100',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入进场数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
  {
    field: 'actualQuantity',
    title: '实收数量',
    minWidth: '100',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入实收数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
  {
    field: 'appearanceDescription',
    title: '外观质量描述',
    minWidth: '100',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入外观质量描述',
      },
    },
    // slots: {
    //   default: 'createAt',
    // },
  },
  {
    field: 'remark',
    title: '备注',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入外观质量描述',
      },
    },
    minWidth: '100',
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  loading: false,
  cellClassName: ({ row, column }: any) => {
    return column.editRender || !row.id ? '' : 'bg-gray-100';
  },
  editRules: {
    siteEntryQuantity: [{ required: true, message: '请输入进场数量' }],
    actualQuantity: [{ required: true, message: '请输入实收数量' }],
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',

            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',

            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, rowIndex, row }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            item.disabled = rowIndex === tableOptions.data.length - 2;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = rowIndex === 0;
            break;
          }
        }
        if (!localEditable.value) {
          item.disabled = true;
        }
      });
      return !!row.id;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      // 如果没有id则证明是内置数据
      if (!row.id) {
        return false;
      }
      // 版本数据已启用无法进行编辑
      if (!localEditable.value) {
        ElMessage.warning('当前数据已提交,不可编辑');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
    const optionalUnitArr = row?.optionalUnits
      ? row.optionalUnits.split(',')
      : [];
    const options = optionalUnitArr.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });

    unitOptions.value = options;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem);
  },
  async menuClick({
    menu,
    rowIndex,
    row,
  }: {
    menu: any;
    row: any;
    rowIndex: any;
  }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionDetail(id);
        if (res) {
          refreshData();
          ElMessage.success('删除成功');
        }
      });
    }
    if (menu.code === 'MOVE_DOWN') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex + 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      refreshData();
    }
    if (menu.code === 'MOVE_UP') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex - 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      refreshData();
    }
  },
  // 完成编辑
  async editClosed({ row }: any) {
    const data = {
      id: row.id,
      qualityStandard: row.qualityStandard,
      unit: row.unit,
      siteEntryQuantity: row.siteEntryQuantity,
      actualQuantity: row.actualQuantity,
      appearanceDescription: row.appearanceDescription,
      orderNo: row.orderNo,
      remark: row.remark,
    };
    const res = await editInspectionDetail(data);
    if (res) {
      refreshData();
    }
  },
};
// 刷新数据
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem);
}

// 确认提交
async function insureSubmit() {
  if (localInfoData.value.auditStatus === AuditStatus.APPROVED) {
    ElMessage.warning('当前数据已通过审批,不可编辑');
    return;
  }
  if (localInfoData.value.auditStatus === AuditStatus.AUDITING) {
    ElMessage.warning('当前数据审批中,不可编辑');
    return;
  }

  if (localInfoData.value.submitStatus === SubmitStatus.PENDING) {
    // 需要提交数据了
    const isPassForm = checkForm();
    if (!isPassForm) {
      return;
    }

    if (tableOptions.data.length <= 1) {
      ElMessage.warning('请添加验收材料');
      return;
    }

    const validateRows = tableOptions.data.filter((v: any) => !v.internal);
    if (await tableRef.value.validate(validateRows)) {
      return;
    }

    // if (
    //   tableOptions.data.some(
    //     (v: any) => !v.siteEntryQuantity || !v.actualQuantity,
    //   )
    // ) {
    //   ElMessage.warning('');
    //   return;
    // }
  }

  const submitStatus =
    localInfoData.value.submitStatus === SubmitStatus.PENDING
      ? SubmitStatus.SUBMITTED
      : SubmitStatus.PENDING;
  const submitStatusText =
    localInfoData.value.submitStatus === SubmitStatus.PENDING ? '提交' : '取消';
  const { id } = formData.value;

  const data = {
    id,
    submitStatus, // 修改提交状态为已提交
  };

  const res = await editInspectionBill(data);
  if (res) {
    localInfoData.value.submitStatus = submitStatus;

    ElMessage.success(`${submitStatusText}成功`);
    emit('refresh');
  }
}

// 确认修改
async function insureSave() {
  const year = Number(dayjs(formData.value.entryDate).format('YYYY'));
  const month = Number(dayjs(formData.value.entryDate).format('M'));
  const day = Number(dayjs(formData.value.entryDate).format('D'));
  const params = {
    id: formData.value.id,
    purchaseType: formData.value.purchaseType,
    supplierId: formData.value.supplierId,
    supplierName: formData.value.supplierName,
    contractId: formData.value.contractId,
    contractName: formData.value.contractName,

    year,
    month,
    day,
  };

  await editInspectionBill(params);
}

// 供应商改变
async function supplierChange() {
  if (tableOptions.data.length > 1) {
    // 还原为旧值
    nextTick(() => {
      formData.value.supplierId = oldFormData.value.supplierId;
      formData.value.supplierName = oldFormData.value.supplierName;
    });

    ElMessage.warning('请先删除材料数据后再修改');
  } else {
    const data = contractAndSupplierOptions.value.find(
      (item: any) => item.id === formData.value.supplierId,
    );
    formData.value.supplierName = data.name;
    contractOptions.value = data?.contracts
      ? data.contracts.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        })
      : [];

    if (contractOptions.value.length === 1) {
      formData.value.contractId = contractOptions.value[0].value;
      formData.value.contractName = contractOptions.value[0].label;
    } else {
      formData.value.contractId = '';
      formData.value.contractName = '';
    }

    await insureSave();
  }
}

// 合同改变
async function contractChange() {
  if (tableOptions.data.length > 1) {
    // 还原为旧值
    nextTick(() => {
      formData.value.contractId = oldFormData.value.contractId;
      formData.value.contractName = oldFormData.value.contractName;
    });

    ElMessage.warning('请先删除材料数据后再修改');
  } else {
    const data = contractOptions.value.find(
      (item: any) => item.value === formData.value.contractId,
    );

    formData.value.contractName = data.label;

    await insureSave();
  }
}

// 弹窗打开回调
async function handleOpen() {}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
}

// 传递清单列表
const goodList = ref([]);
provide('goodList', goodList);
// 获取表格数据
async function getList() {
  const { id } = localInfoData.value;

  const res = await getInspectionDetailList(id);
  res.forEach((v: any, i: number) => {
    v.index = i + 1;
  });
  tableOptions.data =
    res.length === 0 ? [emptyGoodsItem] : [...res, emptyGoodsItem];
}

// 点击穿梭框
async function transferDataClick() {
  if (!checkForm()) {
    return;
  }

  const isPARTY_A_SUPPLIED =
    formData.value.purchaseType === PurchaseType.PARTY_A_SUPPLIED;
  const isTRANSFER_IN =
    formData.value.purchaseType === PurchaseType.TRANSFER_IN;
  const isLXSUPPLIED = formData.value.supplierName === '零星材料供应商';

  // 甲供 调拨 还有 甲指 中的零星供应商 是 选择材料字典 否则是合同材料
  curTab.value =
    isPARTY_A_SUPPLIED || isTRANSFER_IN || isLXSUPPLIED
      ? 'MATERIAL_DICT'
      : 'CONTRACT';

  addMaterialinfoData.value = {
    inspectionBillId: formData.value.id,
    materialSearchType: curTab.value,
    purchaseType: formData.value.purchaseType,
    contractId: formData.value.contractId,
  };

  addOrEditMaterialVisible.value = true;
}

function checkForm() {
  // 如果为甲供必须选择供应商名称
  if (
    formData.value.purchaseType === PurchaseType.TRANSFER_IN &&
    !formData.value.supplierId
  ) {
    ElMessage.warning('请选择供应商名称');
    return false;
  }
  // 如果为 甲指/自采/集采 必须选择供应商名称 和 合同名称 除非
  if (
    [
      PurchaseType.CENTRALIZED_PURCHASE,
      PurchaseType.PARTY_A_DIRECTED,
      PurchaseType.SELF_PURCHASE,
    ].includes(formData.value.purchaseType)
  ) {
    if (!formData.value.supplierId) {
      ElMessage.warning('请选择供应商名称');
      return false;
    }

    if (
      formData.value.supplierName !== '零星材料供应商' &&
      !formData.value.contractId
    ) {
      ElMessage.warning('请选择合同名称');
      return false;
    }
  }

  return true;
}

// 关闭点击
async function closeClick() {
  if (localEditable.value) {
    emit('refresh');
  }

  drawerVisible.value = false;
}

// 获取供应商名称
async function getSupplierContractlList() {
  const params = {
    purchaseType: formData.value.purchaseType as PurchaseTypeEnum,
  };
  const res = await getSupplierAndContractList(params);
  contractAndSupplierOptions.value = res;

  supplierOptions.value = res.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
}

// 获取附件列表
async function getAnnexlist() {
  const id = localInfoData.value.id;
  const res = await getAttachmentList(id);

  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);

  fileList.value = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
}
// 新增附件
async function addAnnex(data: any) {
  const _data = {
    incomingInspectionId: localInfoData.value.id,
    fileName: data.fileName,
    fileKey: data.fileKey,
    fileSize: Number(data.fileSize),
    fileExt: data.fileExt,
    fileContentType: data.fileContentType,
  };

  const res = await addInspectionBillAttachment(_data);
  if (res) {
    ElMessage.success('添加成功');
    getAnnexlist();
  }
}
// 移除附件
async function removeAnnex(data: any) {
  const id = data.id;
  const res = await delAttachmen(id);
  if (res) {
    ElMessage.success('删除成功');
  }
}

// async function init() {
//   await getAnnexlist();
// }

const prevBtn = () => {
  emit('move', -1);
};
const nextBtn = () => {
  emit('move', 1);
};

// onBeforeMount(() => {
//   init();
// });
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
.el-form-item--large {
  margin-bottom: 16px;
}
</style>
