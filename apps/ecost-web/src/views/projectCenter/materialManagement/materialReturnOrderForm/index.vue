<template>
  <Page class="ml-4 mt-4 h-full rounded bg-white">
    <div class="flex h-full">
      <div class="area-left h-full pr-2">
        <TimeSelect
          ref="timeSelectEl"
          :time-data="timeSelectData"
          @select="timeSelect"
        />
      </div>
      <div class="area-right h-full flex-1 overflow-auto">
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <template #top>
            <div class="flex h-[48px] items-center justify-between gap-4">
              <div class="mb-2 flex items-center gap-2">
                <ElButton
                  type="primary"
                  size="small"
                  @click="debounceAddData"
                  v-auth="actionPermissions.apCreate"
                >
                  新增
                </ElButton>
                <ElButton type="default" size="small" @click="filter">
                  重置筛选
                </ElButton>
              </div>
              <div class="flex items-center gap-4">
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出单据
                  </ElButton>
                </div>
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出列表
                  </ElButton>
                </div>
              </div>
            </div>
          </template>

          <template #seq="{ $rowIndex }">
            <div>{{ $rowIndex + 1 }}</div>
          </template>
          <template #code="{ row }">
            <div>
              <ElButton
                size="small"
                type="primary"
                link
                @click="openDetail(row)"
              >
                {{ row.code }}
              </ElButton>
            </div>
          </template>

          <template #purchaseType="{ row }">
            <div>
              {{ getPurchaseTypeLabel(row.purchaseType) }}
            </div>
          </template>

          <template #materialSettlementStatus="{ row }">
            <div
              :class="{
                'text-green-500':
                  row.materialSettlementStatus ===
                  MaterialSettlementStatus.SETTLED,
                'text-red-500':
                  row.materialSettlementStatus ===
                  MaterialSettlementStatus.UN_SETTLED,
              }"
            >
              {{
                getMaterialSettlementStatusLabel(row.materialSettlementStatus)
              }}
            </div>
          </template>

          <template #createAt="{ row }">
            <div>
              {{
                dayjs(`${row.year}/${row.month}/${row.day}`).format(
                  'YYYY-MM-DD',
                )
              }}
            </div>
          </template>

          <template #amount="{ row }">
            {{ amountFormat({ cellValue: row.amount }) }}
          </template>

          <template #submitStatus="{ row }">
            <div
              :class="{
                'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                'text-red-500': row.submitStatus === SubmitStatus.PENDING,
              }"
            >
              {{ getSubmitStatusLabel(row.submitStatus) }}
            </div>
          </template>
          <template #auditStatus="{ row }">
            <div
              :class="{
                'text-red-500': row.auditStatus === AuditStatus.PENDING,
                'text-orange-500':
                  row.auditStatus === AuditStatus.AUDITING ||
                  row.auditStatus === AuditStatus.REJECTED,
                'text-green-500': row.auditStatus === AuditStatus.APPROVED,
              }"
            >
              {{ getAuditStatusLabel(row.auditStatus) }}
            </div>
          </template>
          <template #qrcode="{ row }">
            <ElPopover
              placement="left"
              trigger="click"
              v-if="row.submitStatus === SubmitStatus.SUBMITTED"
            >
              <div>
                <QrcodeVue :value="row.code" :size="120" />
              </div>
              <template #reference>
                <div class="flex justify-center">
                  <QrcodeVue :value="row.code" :size="28" />
                </div>
              </template>
            </ElPopover>
          </template>
        </VxeGrid>
      </div>
    </div>

    <EditDrawer
      v-model:visible="drawerVisible"
      :info-data="infoData"
      :editable="editable"
      @move="contractSelectMove"
      @refresh="refreshData"
    />
  </Page>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElMessageBox, ElPopover } from 'element-plus';
import _ from 'lodash';
import QrcodeVue from 'qrcode.vue';

import {
  addMaterialReturnSalesForm,
  changeAuditStatus,
  deleteMaterialReturnSalesForm,
  getMaterialReturnSalesFormList,
  getTimeList,
} from '#/api/projectCenter/projectCenter/materialReturnSalesForm';
import TimeSelect from '#/components/TimeSelect/TimeSelect.vue';
import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getMaterialSettlementStatusLabel,
  getPurchaseTypeLabel,
  getSubmitStatusLabel,
  MaterialSettlementStatus,
  materialSettlementStatusOption,
  purchaseTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import {
  amountFormat,
  findNextOrPrevRow,
  resetFilter,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

import EditDrawer from './components/EditDrawer.vue';

defineOptions({
  name: 'MaterialEntryCheck',
});

const { actionPermissions } = getCurrentPremission();

const timeSelectData = ref([]);

// 新增弹窗是否展示
// const addFromDialogVisible = ref(false);

// 侧抽屉数据
const drawerVisible = ref(false);
const infoData = ref();
const editable = ref(true); // 是否可编辑

const timeSelectEl = ref();
const tableRef = ref();

// const addOrEditContractForm = ref<any>({
//   // 新增/编辑表单
//   id: null,
//   name: '',
//   code: '',
//   contractId: '',
//   supplierId: '',
//   supplierName: '',
//   contractName: '',
//   amount: '',
//   contractTemplateId: '',
//   year: '',
//   month: '',
//   day: '',
//   purchaseType: '',
//   proposedStatus: '',
// });

// 当前选择的时间
const currentTimeItem = ref();

// 当前点击项
const currentItem = ref<any>();

// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    fixed: 'left',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'materialSettlementStatus',
    title: '结算状态',
    width: '150',
    fixed: 'left',
    slots: {
      default: 'materialSettlementStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: materialSettlementStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择结算状态',
      },
    },
  },
  {
    field: 'code',
    title: '单据编码',
    width: '150',
    fixed: 'left',
    slots: {
      default: 'code',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'supplierName',
    title: '供应商名称',
    width: '200',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'contractName',
    title: '合同名称',
    width: '200',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    minWidth: '200',
    slots: {
      default: 'purchaseType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: purchaseTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择采购类型',
      },
    },
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '200',
  },
  {
    field: 'amount',
    title: '金额',
    width: '200',
    slots: {
      default: 'amount',
    },
  },
  {
    field: 'creator',
    title: '编制人',
    width: '150',
  },
  {
    field: 'returnOrderDate',
    title: '退货日期',
    width: '150',
    // slots: {
    //   default: 'returnOrderDate',
    // },
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '150',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审核状态',
    width: '150',
    editRender: {
      name: 'VxeSelect',
      options: auditStatusOption,
    },
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
  {
    title: '二维码',
    width: '150',
    slots: {
      default: 'qrcode',
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled =
              !actionPermissions.apDelete ||
              row.submitStatus === SubmitStatus.SUBMITTED;
            break;
          }

          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前无修改权限');
        return;
      }
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交数据');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;

    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await deleteMaterialReturnSalesForm(id);
        if (res) {
          await refreshData();
          await getTimeSelectData();
          ElMessage.success('删除成功');
        }
      });
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (column.field === 'auditStatus') {
      const id = row.id;
      const item = auditStatusOption.find(
        (item) => item.value === row.auditStatus,
      );
      ElMessageBox.confirm(`你确定要${item?.label}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const data = {
            id,
            auditStatus: row.auditStatus, // 修改提交状态为已提交
          };

          const res = await changeAuditStatus(id, data);
          if (res) {
            refreshData();
            ElMessage.success(`${item?.label}成功`);
          }
        })
        .catch(async () => {
          refreshData();
        });
    }
  },
};
// 新增数据
async function addData() {
  const res = await addMaterialReturnSalesForm();
  const itemData = {
    id: res.id,
    orgName: res.projectName,
    auditStatus: res.auditStatus,
    code: res.code,
    creator: res.creator,
    day: res.day,
    month: res.month,
    year: res.year,
    purchaseType: res.purchaseType,
    submitStatus: res.submitStatus,
    materialSettlementStatus: res.materialSettlementStatus,

    supplierId: '',
    supplierName: '',
    contractId: '',
    contractName: '',
  };
  infoData.value = itemData;
  currentItem.value = itemData;
  await refreshData();
  await getTimeSelectData();
  drawerVisible.value = true;
}

async function filter() {
  const $grid = tableRef.value;
  resetFilter(tableOptions.columns, $grid);
}

const debounceAddData = _.debounce(addData, 500);

// 打开详情
async function openDetail(row: any, isOpen = true) {
  infoData.value = {
    id: row.id,
    orgName: row.projectName,
    auditStatus: row.auditStatus,
    code: row.code,
    creator: row.creator,
    day: row.day,
    month: row.month,
    year: row.year,

    purchaseType: row.purchaseType,
    submitStatus: row.submitStatus,
    materialSettlementStatus: row.materialSettlementStatus,

    supplierId: row.supplierId,
    supplierName: row.supplierName,
    contractId: row.contractId,
    contractName: row.contractName,
  };
  if (isOpen) {
    drawerVisible.value = true;
  }
}

// 合同选中移动
async function contractSelectMove(move: number) {
  const $grid = tableRef.value;
  const targetItem = findNextOrPrevRow($grid, move);
  if (targetItem) {
    tableEvents.cellClick({ row: targetItem });
    setCurrentRow(tableOptions.data, tableRef.value, targetItem);
    openDetail(targetItem, false);
  }
}

// 刷新
async function refreshData() {
  await getList();
  await getTimeSelectData();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
}

// 时间选择
async function timeSelect(row: any) {
  currentTimeItem.value = row;

  await getList();
}

// 获取时间选择框数据 并展开
async function getTimeSelectData() {
  const res = await getTimeList();

  timeSelectData.value = res;
}

// 初始化
async function init() {
  await getTimeSelectData();

  await getList();
}
// 获取退货单数据
async function getList() {
  tableOptions.loading = true;

  const params: any = {};
  if (currentTimeItem.value?.year) {
    params.year = currentTimeItem.value.year;
  }
  if (currentTimeItem.value?.month) {
    params.month = currentTimeItem.value.month;
  }
  if (currentTimeItem.value?.day) {
    params.day = currentTimeItem.value.day;
  }
  const res = await getMaterialReturnSalesFormList(params);

  for (const item of res) {
    item.returnOrderDate = `${item.year}-${item.month}-${item.day}`;
  }
  tableOptions.data = res;
  tableOptions.loading = false;
}
// function handleDropDownItem(command: string) {
//   if (command === 'download') {
//     // TODO 模版下载
//     downloadLocalFile('/file/费用字典发布.xlsx', '费用字典发布.xlsx');
//   }
// }
// 渲染前
onBeforeMount(async () => {
  init();
});
</script>

<style lang="scss">
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
