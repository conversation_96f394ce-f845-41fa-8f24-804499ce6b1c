<template>
  <ElDialog
    v-model="dialogVisible"
    :destroy-on-close="false"
    :title="title"
    @close="dialogClosed"
    top="2%"
    :style="{ width: '80%' }"
  >
    <TransferSelector
      v-model:selection-data="selectionData"
      :choice-class-data="choiceClassData"
      :choice-detail-data="choiceDetailData"
      :cur-tab="curTab"
      @select="classSelect"
      @select-all="classSelectAllItem"
    />

    <template #footer>
      <ElButton @click="dialogClosed">取消</ElButton>
      <ElButton type="primary" @click="submit">确定</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { inject, onBeforeMount, ref, watch } from 'vue';

import { ElButton, ElDialog, ElMessage } from 'element-plus';

import {
  getMaterialCategoryList,
  getMaterialDetailList,
  saveMaterialReturnSalesFormMaterialDetail,
} from '#/api/projectCenter/projectCenter/materialReturnSalesForm';

import TransferSelector from './TransferSelector/index.vue';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}

const props = withDefaults(
  defineProps<{
    infoData: {
      returnSalesFormId: string;
    };
    title: string;
    visible: boolean;
  }>(),
  {
    title: '',
    visible: false,
    infoData: () => {
      return {
        returnSalesFormId: '',
      };
    },
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

// 当前选择的数据
const curTab = inject<any>('curTab');

// 选择区的分类数据
const choiceClassData = ref<any>([]);
// 选择区的明细数据
const choiceDetailData = ref<any>([]);
// 确认区的数据
const selectionData = inject<any>('goodList');
// 传递的表单
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
    // 每次传值的时候调用分类列表
    getCategoryList();
  },
  { deep: true, immediate: true },
);
// 弹窗是否展示
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);
// 获取分类列表
async function getCategoryList() {
  if (!localInfoData.value.returnSalesFormId) return;

  const res = await getMaterialCategoryList(
    localInfoData.value.returnSalesFormId,
  );
  choiceClassData.value = res || [];
}
async function classSelectAllItem(row: any) {
  console.log(row);
}

async function classSelect(row: any) {
  getDetailList(row.id);
}
// 获取明细列表
async function getDetailList(categoryId: string) {
  const returnSalesFormId = localInfoData.value.returnSalesFormId;

  // const ids = new Set(selectionData.value.map((v: any) => v.id));
  const res = await getMaterialDetailList(returnSalesFormId, categoryId);

  const data = res.map((item: any) => {
    // const selected = !!ids.has(item.id);
    return {
      ...item,
      specificationModel: item.spec,
      meteringUnit: item.unit,
      selected: false,
    };
  });
  choiceDetailData.value = res ? data : [];
}

// 关闭弹窗
function dialogClosed() {
  selectionData.value = [];
  emit('update:visible', false);
}

// 提交
const submit = async () => {
  const filterSelectionData = selectionData.value.filter(
    (v: any) => !v.disabled,
  );
  const data = [];
  for (const item of filterSelectionData) {
    if (!item.salesReturnQuantity) {
      ElMessage.error(`${item.materialName}:未填写退货数量`);
      return false;
    }
    if (item.salesReturnQuantity > item.inventoryQuantity) {
      ElMessage.error(`${item.materialName}:退货数量不能大于库存数量`);
      return false;
    }
    if (item.salesReturnQuantity < 0) {
      ElMessage.error(`${item.materialName}:退货数量不能小于0`);
      return false;
    }
    data.push({
      materialId: item.materialId,
      materialName: item.materialName,
      materialSpec: item.materialSpec,
      inStockQuantity: item.inventoryQuantity,
      inStockPrice: item.price,
      salesReturnQuantity: item.salesReturnQuantity,
      unit: item.unit,
      remark: item.remark,
    });
  }

  if (data.length <= 0) {
    ElMessage.warning('请先添加材料');
    return;
  }
  const returnSalesFormId = localInfoData.value.returnSalesFormId;

  const res = await saveMaterialReturnSalesFormMaterialDetail({
    list: data,
    returnSalesFormId,
  });

  if (res) {
    ElMessage.success('添加成功');
    emit('refresh');
    emit('update:visible', false);
  }
};
// 初始化
async function init() {}

onBeforeMount(async () => {
  init();
});
</script>
<style scoped lang="scss">
.el-dialog__title {
  color: var(--el-text-color-primary);
  font-size: 12px;
  line-height: var(--el-dialog-font-line-height);
}
</style>
