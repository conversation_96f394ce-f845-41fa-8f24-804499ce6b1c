<template>
  <div class="selections h-full w-full shadow-sm">
    <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
      <div>确认材料</div>
    </div>
    <div class="h-[calc(100%-30px)] w-full">
      <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
        <template #top></template>
        <template #seq="{ $rowIndex }">
          <div>{{ $rowIndex + 1 }}</div>
        </template>
        <!-- 自定义退货数量列的标题 -->
        <template #salesReturnHeader="{ column }">
          <span>{{ column.title }}</span>
          <span class="required-star">*</span>
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import { vxeBaseConfig } from '#/utils/vxeTool';

const props = withDefaults(
  defineProps<{
    selectionData: any;
  }>(),
  {
    selectionData: [],
  },
);

const emit = defineEmits<{
  (e: 'remove', data: any): void;
}>();

// 表格数据
const tableRef = ref();
// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '100',
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '100',
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '150',
  },
  {
    field: 'inventoryQuantity',
    title: '在库数量',
    width: '150',
  },
  {
    field: 'price',
    title: '单价',
    width: '150',
  },
  {
    field: 'salesReturnQuantity',
    title: '退货数量',
    width: '150',
    editable: true,
    slots: {
      header: 'salesReturnHeader',
    },
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入退货数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  editConfig: {
    mode: 'cell', // 单元格编辑模式
    trigger: 'dblclick', // 双击触发编辑
  },
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  cellClassName: ({ column }: any) => {
    return column.editRender ? '' : 'bg-gray-100';
  },
  editRules: {
    salesReturnQuantity: [
      {
        required: true,
        message: '请输入小数位<=8位的数字',
        pattern: /^\d+(\.\d{1,8})?$/,
      },
    ],
  },
  columns,
  data: [],
});

watch(
  () => props.selectionData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    deep: true,
    immediate: true,
  },
);
// 表格事件
const gridEvents = {
  cellDblclick({ row, rowIndex, column }: any) {
    if (row.disabled && column.field !== 'salesReturnQuantity') {
      // 否则，触发移除
      emit('remove', { row, rowIndex });
    }
  },
  // 添加单元格点击事件，确保编辑状态可以保持
  cellClick({ row, column }: any) {
    if (column.field === 'salesReturnQuantity' && row._isEdit) {
      // 如果单元格已处于编辑状态，则保持编辑
      return false;
    }
  },
};
</script>
<style scoped lang="scss">
.required-star {
  color: #f56c6c;
  margin-left: 4px;
}
</style>
