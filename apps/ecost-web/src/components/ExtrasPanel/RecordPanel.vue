<template>
  <div class="h-full">
    <div class="header flex items-center justify-between pb-4 pt-2">
      <div class="title text-[18px]">变更记录</div>
      <div @click="panelClose" class="cursor-pointer text-[18px]">
        <ElIcon><Close class="text-black" /></ElIcon>
      </div>
    </div>
    <div class="content h-[calc(100%_-_60px)] overflow-auto">
      <div v-if="localRecordList && localRecordList.length > 0">
        <div
          class="mb-4 rounded-[6px] bg-white p-4"
          v-for="(item, idx) in localRecordList"
          :key="idx"
        >
          <div class="flex justify-between">
            <div>{{ item.name }}</div>
            <div>{{ dayjs(item.time).format('YYYY年MM月DD日 HH点mm分') }}</div>
            <div class="text-orange-400">有{{ item.data?.length }}处修改</div>
          </div>
          <div class="mt-4" v-for="v in item.data" :key="v.id">
            <div class="mb-2">{{ filedKeyName[v.fieldKey] }}:</div>
            <div
              class="mb-1 min-h-[28px] rounded bg-red-200 pl-2 text-xs leading-[28px] line-through"
            >
              {{ v.oldValue }}
            </div>
            <div
              class="min-h-[28px] rounded bg-green-200 pl-2 text-xs leading-[28px]"
            >
              {{ v.newValue }}
            </div>
          </div>
        </div>
      </div>
      <div class="mb-4 rounded-[6px] bg-white p-4" v-else>暂无变更记录</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

import { Close } from '@element-plus/icons-vue';
import { dayjs, ElIcon } from 'element-plus';

// 传入的数据类型
interface RecordListType {
  id: string; // id
  createAt: string; // 创建时间
  createByName: string; // 创建人
  versionNumber: string; // 版本号 用于按照其数据进行组合
  fieldKey: string; // 字段key
  newValue: string; // 新值
  oldValue: string; // 旧值
}
// 处理后的数据类型
interface LocalRecordListType {
  name: string;
  time: string;
  versionNumber: string;
  data: RecordListType[];
}
const props = defineProps<{
  filedKeyName: Record<string, string>; // 传入的 字段key 和 字段名称 的映射
  recordList: RecordListType[]; // 传入的数据
}>();

const emit = defineEmits(['close']);

const localRecordList = ref<LocalRecordListType[] | null>(null);
watch(
  () => props.recordList,
  (val) => {
    localRecordList.value = dealChangeLog(val);
  },
  {
    immediate: true,
  },
);

function dealChangeLog(records: RecordListType[]): LocalRecordListType[] {
  const groups: Record<string, RecordListType[]> = {};

  // 按 versionNumber 分组
  for (const record of records) {
    const version = record.versionNumber;
    if (!groups[version]) {
      groups[version] = [];
    }
    groups[version].push(record);
  }

  // 格式化为目标结构
  const result = Object.entries(groups).map(([versionNumber, data]) => {
    const [first] = data; // 安全解构
    return {
      versionNumber,
      time: first?.createAt ?? '', // 安全访问
      name: first?.createByName ?? '',
      data,
    };
  });

  // 按时间倒序排序
  result.sort(
    (a, b) => new Date(b.time).getTime() - new Date(a.time).getTime(),
  );

  return result;
}

function panelClose() {
  emit('close');
}
</script>
