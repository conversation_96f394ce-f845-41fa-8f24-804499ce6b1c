<template>
  <div class="h-full">
    <div class="header flex items-center justify-between pb-4 pt-2">
      <div class="title text-[18px]">追溯明细</div>
      <div @click="$emit('close')" class="cursor-pointer text-[18px]">
        <ElIcon><Close class="text-black" /></ElIcon>
      </div>
    </div>
    <div class="extras mb-1 flex">
      <div class="mr-4 flex gap-1" v-show="isTree">
        <ElButton type="text" @click="openAll">展开</ElButton>
        <ElButton type="text" @close="closeAll">折叠</ElButton>
      </div>
      <div class="flex content-center items-center justify-between gap-2">
        <slot name="traceDetailHeader"></slot>
      </div>
    </div>
    <div class="content h-[calc(100%_-_80px)]">
      <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
        <template #name="{ row }">
          <ElButton type="text" @click="jumpTo(row)">
            {{ row.name }}
          </ElButton>
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';

import { Close } from '@element-plus/icons-vue';
import { ElButton, ElIcon, ElMessageBox } from 'element-plus';

import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

const props = defineProps<{
  columns: any[];
  data: any[];
  editable: boolean;
  infoData: any;
  isTree?: boolean;
}>();
const emit = defineEmits(['close', 'remove', 'jump']);

const tableRef = ref();
const currentItem = ref();

const localEditable = ref<Boolean>(props.editable);
watch(
  () => props.editable,
  (nval) => {
    localEditable.value = nval;
  },
);

const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
  },
);

const columns = computed(() => {
  return props.columns.map((v) => {
    return v;
  });
});

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  loading: false,
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      // 内置节点控制
      if (row?.disabled || !localEditable.value) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      return true;
    },
  },
  columns,
  data: props.data,
});
watch(
  () => props.data,
  (val) => {
    tableOptions.data = val;
  },
  { immediate: true },
);
const tableEvents = {
  cellClick({ row }: any) {
    currentItem.value = row;
  },
  // 右键菜单
  cellMenu({ row }: any) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, currentItem.value, tableRef.value);
  },
  async menuClick({ menu, row }: any) {
    switch (menu.code) {
      case 'DELETE_ROW': {
        ElMessageBox.confirm('确定删除该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          emit('remove', { row });
        });
        break;
      }
    }
  },
};

function openAll() {
  const $grid = tableRef.value;
  if ($grid) {
    $grid.setAllTreeExpand(true);
  }
}

function closeAll() {
  const $grid = tableRef.value;
  if ($grid) {
    $grid.clearTreeExpand();
  }
}

const jumpTo = (row: any) => {
  emit('jump', { row });
};
</script>
