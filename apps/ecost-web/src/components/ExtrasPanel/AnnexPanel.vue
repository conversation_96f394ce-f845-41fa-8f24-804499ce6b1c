<template>
  <div class="h-full">
    <div class="header flex items-center justify-between pb-4 pt-2">
      <div class="title text-[18px]">附件</div>
      <div @click="panelClose" class="cursor-pointer text-[18px]">
        <ElIcon><Close class="text-black" /></ElIcon>
      </div>
    </div>
    <div class="content h-[calc(100%_-_60px)] overflow-scroll">
      <BaseUpload
        v-bind="attrs"
        v-model:file-list="localFileList"
        list-type="picture"
        @success="(data) => emit('success', data)"
        @remove="(file) => emit('remove', file)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, useAttrs, watch } from 'vue';

import { Close } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';

import BaseUpload from '#/components/BaseUpload/BaseUpload.vue';

const props = withDefaults(
  defineProps<{
    fileList?: any;
  }>(),
  {
    fileList: [], // 文件列表数据
  },
);

const emit = defineEmits<{
  (e: 'update:fileList', fileList: any): void;
  (e: 'success', data: any): void;
  (e: 'remove', file: any): void;
  (e: 'close'): void;
}>();
const attrs = useAttrs();
const localFileList = ref(props.fileList);
watch(
  () => props.fileList,
  (val) => {
    localFileList.value = [...val];
  },
  {
    immediate: true,
  },
);

function panelClose() {
  emit('close');
}
</script>
