<template>
  <div
    v-if="visible"
    class="fixed z-50 overflow-hidden rounded-lg border border-gray-300 bg-white shadow-lg"
    :style="dialogStyle"
  >
    <!-- 关闭按钮 -->
    <!-- 顶部拖拽区域 -->
    <div
      class="cursor-move border-b bg-gray-100 pb-2 pl-3 pr-3 pt-2 text-sm font-bold"
      @mousedown="startDrag"
    >
      AI提取
      <span
        class="float-right cursor-pointer hover:text-red-500"
        @click="dialogClose"
      >
        ✕
      </span>
    </div>

    <!-- 内容区域 -->
    <div class="box-border flex h-full w-full gap-4 p-4">
      <div class="flex w-[60%] items-center justify-center">
        <img
          class="h-[80%] max-h-[600px] object-contain"
          :src="imageUrl"
          draggable="false"
        />
      </div>
      <div class="w-[40%] overflow-auto bg-gray-50 p-4">
        <div>{{ imageText }}</div>
      </div>
    </div>

    <!-- 缩放角 -->
    <div
      class="absolute bottom-0 right-0 h-4 w-4 cursor-se-resize"
      @mousedown.stop="startResize"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, reactive } from 'vue';

defineProps<{
  imageText: string;
  imageUrl: string;
  visible: boolean;
}>();
const emit = defineEmits(['close']);

const position = reactive({ left: 300, top: 200 });
const size = reactive({ width: 800, height: 500 });

const dialogStyle = computed(
  () =>
    ({
      position: 'fixed',
      left: `${position.left}px`,
      top: `${position.top}px`,
      width: `${size.width}px`,
      height: `${size.height}px`,
      zIndex: 99,
      minWidth: '400px',
      minHeight: '200px',
    }) as const,
);

let dragging = false;
let resizing = false;
let offsetX = 0;
let offsetY = 0;

const startDrag = (e: MouseEvent) => {
  dragging = true;
  offsetX = e.clientX - position.left;
  offsetY = e.clientY - position.top;
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopAction);
};

const startResize = (e: MouseEvent) => {
  resizing = true;
  offsetX = e.clientX;
  offsetY = e.clientY;
  document.addEventListener('mousemove', onResize);
  document.addEventListener('mouseup', stopAction);
};

const onDrag = (e: MouseEvent) => {
  if (!dragging) return;
  position.left = e.clientX - offsetX;
  position.top = e.clientY - offsetY;
};

const onResize = (e: MouseEvent) => {
  if (!resizing) return;
  const dx = e.clientX - offsetX;
  const dy = e.clientY - offsetY;
  size.width += dx;
  size.height += dy;
  offsetX = e.clientX;
  offsetY = e.clientY;
};

const dialogClose = () => {
  emit('close');
};

const stopAction = () => {
  dragging = false;
  resizing = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('mouseup', stopAction);
};

onBeforeUnmount(() => stopAction());
</script>

<style scoped lang="scss"></style>
