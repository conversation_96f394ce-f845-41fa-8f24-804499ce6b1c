<template>
  <div
    class="z-100 absolute top-0 h-full w-[400px] rounded-[12px] bg-[#eee] p-4 text-[14px]"
    :style="{
      right: shouldLogShow ? '0' : '-400px',
      transition: 'right 0.3s cubic-bezier(0.4,0,0.2,1)',
    }"
  >
    <div class="btnGroup absolute left-[-26px] top-[10%]">
      <div
        v-for="v in localOptions"
        :key="v.key"
        :class="`flex min-h-[68px] w-[26px] cursor-pointer items-center justify-center border border-gray-400 p-[4px] text-center text-[12px] text-white ${v.color}`"
        @click="changeTab(v.key)"
      >
        {{ v.name }}
      </div>
    </div>
    <!-- 变更记录功能 -->
    <div class="h-[100%]" v-if="curTab === 'RECORD'">
      <div class="header flex items-center justify-between pb-6 pt-2">
        <div class="title text-[18px]">变更记录</div>
        <div
          @click="changeLogStatus('close')"
          class="btn-close cursor-pointer text-[18px]"
        >
          <ElIcon>
            <Close class="text-black" />
          </ElIcon>
        </div>
      </div>

      <div class="content h-[calc(100%_-_60px)]">
        <div v-if="localRecordList?.length > 0">
          <div
            class="mb-4 rounded-[6px] bg-white p-4"
            v-for="item in localRecordList"
            :key="item.id"
          >
            <div class="flex justify-between">
              <div>{{ item.name }}</div>
              <div>
                {{ dayjs(item.time).format('YYYY年MM月DD日 HH点mm分') }}
              </div>
              <div class="text-orange-400">有{{ item.data?.length }}处修改</div>
            </div>
            <div class="mt-4" v-for="(v, i) in item.data" :key="i">
              <div class="mb-2">{{ filedKeyName[v.fieldKey] }}:</div>
              <div
                class="mb-[4px] min-h-[28px] rounded-[4px] bg-red-200 pl-2 text-[12px] leading-[28px] line-through"
              >
                {{ v.oldValue }}
              </div>
              <div
                class="min-h-[28px] rounded-[4px] bg-green-200 pl-2 text-[12px] leading-[28px]"
              >
                {{ v.newValue }}
              </div>
            </div>
          </div>
        </div>
        <div class="mb-4 rounded-[6px] bg-white p-4" v-else>暂无变更记录</div>
      </div>
    </div>
    <!-- 上传附件功能 -->
    <div class="h-[100%]" v-else-if="curTab === 'ANNEX'">
      <div class="header flex items-center justify-between pb-6 pt-2">
        <div class="title text-[18px]">附件</div>
        <div
          @click="changeLogStatus('close')"
          class="cursor-pointer text-[18px]"
        >
          <ElIcon>
            <Close class="text-black" />
          </ElIcon>
        </div>
      </div>

      <div class="content h-[calc(100%_-_60px)] overflow-scroll">
        <BaseUpload
          v-bind="attrs"
          v-model:file-list="localFileList"
          list-type="picture"
          @success="(data) => emit('success', data)"
          @remove="(file) => emit('remove', file)"
        />
      </div>
    </div>
    <!-- 数据溯源功能 -->
    <div class="h-[100%]" v-else-if="curTab === 'TRACEBACK'">
      <div class="header flex items-center justify-between pb-6 pt-2">
        <div class="title text-[18px]">数据溯源</div>
        <div
          @click="changeLogStatus('close')"
          class="cursor-pointer text-[18px]"
        >
          <ElIcon>
            <Close class="text-black" />
          </ElIcon>
        </div>
      </div>
      <div class="content h-[calc(100%_-_60px)] overflow-scroll">
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents" />
      </div>
    </div>
    <!-- 追溯明细功能 -->
    <div class="h-[100%]" v-else-if="curTab === 'TRACEDETAIL'">
      <div class="header flex items-center justify-between pb-6 pt-2">
        <div class="title text-[18px]">追溯明细</div>
        <div
          @click="changeLogStatus('close')"
          class="cursor-pointer text-[18px]"
        >
          <ElIcon>
            <Close class="text-black" />
          </ElIcon>
        </div>
      </div>
      <div class="content h-[calc(100%_-_60px)] overflow-scroll">
        <VxeGrid ref="tableRef2" v-bind="tableOptions2" v-on="tableEvents2" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, useAttrs, watch } from 'vue';

import { Close } from '@element-plus/icons-vue';
import { dayjs, ElIcon } from 'element-plus';

import BaseUpload from '#/components/BaseUpload/BaseUpload.vue';
import { vxeBaseConfig } from '#/utils/vxeTool';

type visibleOptionType = 'ANNEX' | 'RECORD' | 'TRACEBACK' | 'TRACEDETAIL';

const props = withDefaults(
  defineProps<{
    fileList?: any;
    recordList?: any;
    traceColumns?: any;
    traceDetailColumns?: any;
    traceDetailList?: any;
    traceList?: any;
    visibleOption?: visibleOptionType[];
  }>(),
  {
    visibleOption: () => ['RECORD', 'ANNEX', 'TRACEBACK', 'TRACEDETAIL'],
    recordList: [], // 变更记录数据
    fileList: [], // 文件列表数据
    traceList: [], // 数据溯源数据
    traceColumns: [
      {
        field: 'seq',
        title: '单据编号',
      },
      {
        field: 'createDate',
        title: '日期',
      },
      {
        field: 'create',
        title: '编制人',
      },
    ], // 数据溯源列头数据
    traceDetailColumns: [
      {
        field: 'seq',
        title: '单据编号',
      },
      {
        field: 'createDate',
        title: '日期',
      },
      {
        field: 'create',
        title: '编制人',
      },
    ], // 溯源明细列头数据
    traceDetailList: [], // 溯源明细数据
  },
);

const emit = defineEmits<{
  (e: 'update:fileList', fileList: any): void;
  (e: 'success', data: any): void;
  (e: 'remove', file: any): void;
}>();

const attrs = useAttrs();

const options = [
  {
    key: 'ANNEX',
    name: '附件',
    color: 'bg-blue-500',
  },
  {
    key: 'RECORD',
    name: '变更记录',
    color: 'bg-green-500',
  },
  {
    key: 'TRACEBACK',
    name: '数据溯源',
    color: 'bg-green-500',
  },
  {
    key: 'TRACEDETAIL',
    name: '追溯明细',
    color: 'bg-green-500',
  },
];

const filedKeyName = {
  companyName: '公司名称',
  unifiedSocialCreditCode: '统一社会信用代码',
  registeredAddress: '注册地址',
  companyLocation: '企业所在地',
  taxpayerType: '纳税人身份',
  businessPhone: '业务电话',
  bankName: '开户银行',
  bankAddress: '开户地址',
  bankAccount: '开户账号',
} as any;

const tableRef = ref();
const currentItem = ref();
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  height: '100%',
  loading: false,
  columns: props.traceColumns,
  data: [{ unit: '测试' }],
});
watch(
  () => props.traceList,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
  },
);

const tableEvents = {
  cellClick({ row }: any) {
    currentItem.value = row;
  },
};

const tableRef2 = ref();
const currentItem2 = ref();

const tableOptions2 = reactive<any>({
  ...vxeBaseConfig,
  height: '100%',
  loading: false,
  columns: props.traceDetailColumns,
  data: [],
});
watch(
  () => props.traceDetailList,
  (nval) => {
    tableOptions2.data = nval;
  },
  {
    immediate: true,
  },
);
const tableEvents2 = {
  cellClick({ row }: any) {
    currentItem2.value = row;
  },
};

const localOptions = ref();
watch(
  () => props.visibleOption,
  (nval) => {
    localOptions.value = options.filter((v: any) => {
      return nval.includes(v.key);
    });
  },
  {
    immediate: true,
  },
);

const curTab = ref(props.visibleOption[0]);

const localRecordList = ref(props.recordList);
watch(
  () => props.recordList,
  (val) => {
    localRecordList.value = [...val];
  },
  {
    immediate: true,
  },
);
const localFileList = ref(props.fileList);
watch(
  () => props.fileList,
  (val) => {
    localFileList.value = [...val];
  },
  {
    immediate: true,
  },
);

const shouldLogShow = ref(false);
// 改变变更记录状态
function changeLogStatus(type?: string) {
  shouldLogShow.value = type === 'close' ? false : !shouldLogShow.value;
}

function changeTab(tab: string) {
  if (curTab.value === tab && shouldLogShow.value === true) {
    shouldLogShow.value = false;
  } else {
    shouldLogShow.value = true;
    curTab.value = tab;
  }
}
</script>

<style></style>
