<template>
  <ElForm
    label-position="top"
    @submit.prevent
    ref="formEl"
    :model="data"
    label-width="120px"
    v-bind="Props"
  >
    <template v-for="item in propsComputed" :key="item.prop">
      <ElFormItem
        v-if="item.slot && item.haveLabel"
        :label="item.label"
        :rules="getRules(item as any)"
        :prop="item.prop"
        :class="item.class"
      >
        <slot :name="item.slot"></slot>
      </ElFormItem>
      <slot v-else-if="item.slot" :name="item.slot"></slot>
      <template v-else-if="item.type == 'custom' && !isHide(item.hidden!)">
        <ElFormItem
          v-if="item.haveLabel"
          :label="item.label"
          :rules="getRules(item as any)"
          :prop="item.prop"
          :class="item.class"
        >
          <component
            :is="item.component"
            v-model:data="data[item.prop!]"
            :form-data="form"
            v-bind="item.comProps"
          />
          <span class="tip">{{ item.tip }}</span>
        </ElFormItem>
        <component
          v-else
          :is="item.component"
          v-model:data="data[item.prop!]"
          :form-data="form"
          v-bind="item.comProps"
        />
      </template>
      <ElFormItem
        ref="formItemByPublishRef"
        v-else-if="item.type == 'selectPublish'"
        :label="item.label"
        :rules="getRules(item as any)"
        :prop="item.prop"
        :class="item.class"
      >
        <Publish v-model:data="data[item.prop!]" v-bind="item.comProps" />
      </ElFormItem>
      <ElFormItem
        v-else-if="!isHide(item.hidden!)"
        :label="item.label"
        :rules="getRules(item as any)"
        :prop="item.prop"
        :class="item.class"
      >
        <template v-if="item.type == 'select'">
          <ElSelect
            :teleported="false"
            v-model="data[item.prop!]"
            v-bind="item.comProps"
            @change="() => item.change && item.change()"
          >
            <template v-if="item.isGroupOption">
              <ElOptionGroup
                v-for="group in item.options"
                :key="group.value"
                :label="group.label"
              >
                <ElOption
                  v-for="option in group.options"
                  v-bind="option"
                  :key="option"
                />
              </ElOptionGroup>
            </template>
            <template v-else>
              <ElOption
                v-for="option in item.options"
                v-bind="option"
                :key="option"
              />
            </template>
          </ElSelect>
        </template>
        <template v-else-if="item.type == 'radio'">
          <ElRadioGroup
            @change="() => item.change && item.change()"
            v-model="data[item.prop!]"
            v-bind="item.comProps"
          >
            <el-radio
              v-for="option in item.options"
              :label="option.value"
              :key="option"
            >
              {{ option.label }}
            </el-radio>
          </ElRadioGroup>
        </template>
        <template v-else-if="item.type == 'checkbox'">
          <ElCheckboxGroup v-model="data[item.prop!]" v-bind="item.comProps">
            <el-checkbox
              v-for="option in item.options"
              :label="option.value"
              :key="option"
            >
              {{ option.label }}
            </el-checkbox>
          </ElCheckboxGroup>
        </template>
        <component
          v-else
          v-model="data[item.prop!]"
          :is="getCouponents(item.type)"
          v-bind="item.comProps"
          :placeholder="item.comProps?.placeholder || item.label"
        />
        <span class="tip">{{ item.tip }}</span>
      </ElFormItem>
    </template>
  </ElForm>
</template>

<script lang="tsx" setup>
import { computed, ref, watch } from 'vue';

import {
  ElCascader,
  ElCheckboxGroup,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElOption,
  ElOptionGroup,
  ElRadioGroup,
  ElSelect,
  ElSwitch,
  ElTimePicker,
} from 'element-plus';
import _ from 'lodash';

const Props = withDefaults(
  defineProps<{
    disabled?: boolean;
    form: object;
    props: {
      change?: Function;
      class?: string;
      component?: any; // type为custom时使用外部传入组件
      comProps?: any; // 传入子组件
      haveLabel?: boolean; // 自定义组件是否显示label
      hidden?: boolean | Function;
      isGroupOption?: boolean; // select 选项分组
      label?: string;
      options?: {
        label: string;
        options?: {
          label: string;
          value: any;
        }[];
        value: any;
      }[];
      prop?: string;
      required?: boolean;
      rules?: any;
      slot?: string; // 使用插槽，传入插槽名
      tip?: any;
      type?: (typeof Type)[number];
    }[];
  }>(),
  {},
);

const emit = defineEmits(['update:form']);

const Type = [
  'upload',
  'uploadFile',
  'select',
  'switch',
  'number',
  'radio',
  'custom',
  'datePicker',
  'checkbox',
  'editor',
  'cascader',
  'timePicker',
  'selectPublish',
] as const;

const formEl = ref<any | HTMLElement>();
const data = ref(Props.form) as any;

watch(
  data,
  (newForm) => {
    emit('update:form', newForm);
  },
  { deep: true },
);

const validate = async () => {
  try {
    await formEl.value.validate();
    return true;
  } catch {
    return false;
  }
};

const resetFields = () => {
  return formEl.value.resetFields();
};

const formItemByPublishRef = ref();
const getRules = ({
  label,
  required = false,
  rules,
}: {
  label: string;
  prop: string;
  required: boolean;
  rules: [];
  type?: string;
}) => {
  const res = [] as any;
  if (required) {
    res.push({
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (
          (!_.isNumber(value) && _.isEmpty(value)) ||
          (_.isArray(value) && value.some((it: any) => !it))
        ) {
          callback(new Error(`请提交正确的${label || '内容'}`));
        } else {
          callback();
        }
      },
      trigger: 'change',
    });
  }
  if (rules) {
    return res.concat(rules);
  }
  return res;
};

const getCouponents = (type: string | undefined) => {
  switch (type) {
    case 'cascader': {
      return <ElCascader />;
    }
    case 'datePicker': {
      return <ElDatePicker />;
    }
    case 'number': {
      return <ElInputNumber />;
    }
    case 'switch': {
      return <ElSwitch />;
    }
    case 'timePicker': {
      return <ElTimePicker />;
    }
    default: {
      return <ElInput />;
    }
  }
};

const isHide = (actuator: boolean | Function) => {
  return _.isFunction(actuator)
    ? (actuator as Function)(Props.form, Props)
    : actuator;
};

defineExpose({
  validate,
  resetFields,
  update: (form: any) => {
    data.value = form;
  },
});

const propsComputed = computed(() => {
  return Props.disabled
    ? Props.props.map((item) => {
        return {
          ...item,
          comProps: {
            ...item.comProps,
            disabled: true,
          },
        };
      })
    : Props.props;
});
</script>

<style lang="scss" scoped>
:deep(.ElFormItem__label) {
  align-items: center;
}

:deep(.el-cascader) {
  width: 100%;
}

.tip {
  margin-top: 8px;
  font-size: 12px;
  line-height: 14px;
}
</style>
