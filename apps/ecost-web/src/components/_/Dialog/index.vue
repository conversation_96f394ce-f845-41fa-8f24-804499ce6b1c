<template>
  <ElDialog
    v-model="visible"
    destroy-on-close
    draggable
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="close"
    v-bind="props"
  >
    <slot name="header"></slot>
    <ElScrollbar v-if="height !== 'auto'" :style="{ height }">
      <slot></slot>
    </ElScrollbar>
    <slot v-else></slot>
    <template #footer>
      <slot name="footer"></slot>
      <span class="dialog-footer">
        <ElButton
          :loading="confirmLoading"
          v-if="!hideCancelBtn"
          @click="cancel"
          >{{ cancelTxt }}
        </ElButton>
        <ElButton
          :loading="confirmLoading"
          :disabled="confirmDisabled"
          v-if="!hideConfirmBtn"
          type="primary"
          @click="emit('confirm')"
        >
          {{ confirmTxt }}
        </ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script lang="tsx" setup>
import { ref, watch } from 'vue';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, El<PERSON><PERSON><PERSON>bar } from 'element-plus';

const props = withDefaults(
  defineProps<{
    autoClose?: boolean;
    cancelTxt?: string;
    confirmDisabled?: boolean;
    confirmLoading?: boolean;
    confirmTxt?: string;
    direction?: string;
    height?: string;
    hideCancelBtn?: boolean;
    hideConfirmBtn?: boolean;
    showClose?: boolean;
    visible: boolean;
    width?: string;
  }>(),
  {
    width: '30%',
    cancelTxt: '取消',
    confirmTxt: '确认',
    hideCancelBtn: false,
    hideConfirmBtn: false,
    confirmDisabled: false,
    autoClose: true,
    confirmLoading: false,
    height: 'auto',
    showClose: true,
    direction: 'rtl',
  },
);

const emit = defineEmits(['cancel', 'close', 'confirm', 'update:visible']);

const visible = ref(props.visible);

const close = () => {
  emit('update:visible', false);
  emit('close');
};

const cancel = () => {
  if (props.autoClose) {
    visible.value = false;
  } else {
    emit('cancel');
  }
};

watch(
  () => props.visible,
  () => {
    visible.value = props.visible;
  },
  {
    deep: true,
  },
);
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-right: 30px;
}
</style>
