<template>
  <ElDrawer
    v-bind="$attrs"
    v-model="drawerVisible"
    :modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    :size="sizeNum"
    modal-class="pointer-events-none"
    class="pointer-events-auto"
    @close="handleClose"
    @opened="handleOpen"
  >
    <!-- 头部 -->
    <template #header>
      <div class="layout-header flex h-[60px] items-center justify-between">
        <div class="layout-header-left flex items-center gap-8">
          <div class="flex items-center gap-4">
            <div class="self-center text-sm text-black">合同范本名称</div>
            <ElInput
              size="default"
              v-model="contractTemplateName"
              style="width: 240px"
              placeholder=""
            />
          </div>
          <div class="flex items-center gap-4">
            <div class="self-center text-sm text-black">范本说明</div>
            <ElInput
              size="default"
              v-model="contractTemplateDesc"
              style="width: 240px"
              placeholder=""
            />
          </div>
        </div>
        <div class="btn-group pr-4">
          <ElButton type="primary" size="small" @click="insureSubmit">
            提交
          </ElButton>
        </div>
      </div>
      <ElTooltip :content="isFullScreen ? '收起' : '全屏'" placement="top">
        <IconifyIcon
          @click="isFullScreen = !isFullScreen"
          class="icon-box mr-5 text-2xl outline-none hover:cursor-pointer hover:text-[#006be6]"
          :icon="
            isFullScreen
              ? 'majesticons:arrows-collapse-full'
              : 'majesticons:arrows-expand-full-line'
          "
        />
      </ElTooltip>
    </template>
    <div class="flex bg-[#fafafa]" style="height: calc(100vh - 70px)">
      <!-- 左边区域 -->
      <div class="flex w-[64%] flex-1 flex-col">
        <!-- 中间区域 -->
        <div class="flex flex-1 overflow-hidden bg-[#fafafa]">
          <div ref="wpsRef"></div>
        </div>
      </div>
      <!-- 右侧区域 -->
      <div class="box-border-box flex w-[36%] min-w-[600px] flex-col p-4">
        <div
          class="search-container item-center box-border-box mb-4 flex h-[32px] justify-between"
        >
          <div class="btn-group flex items-center gap-4">
            <ElButton type="primary" size="small" link @click="expand('open')">
              展开
            </ElButton>
            <ElButton type="primary" size="small" link @click="expand('close')">
              折叠
            </ElButton>
          </div>
          <ElInput
            v-model="searchValue"
            style="width: 240px"
            placeholder=""
            :suffix-icon="Search"
          />
        </div>

        <div class="filed-table-container mb-2 flex-1 overflow-y-scroll">
          <VxeGrid ref="tableRef" v-bind="tableOptions">
            <template #top> </template>
            <template #name="{ row, $rowIndex }">
              {{ row.name }}
              <ElButton
                v-if="row.children !== null"
                type="primary"
                link
                size="large"
                :icon="CirclePlus"
              />
            </template>

            <template #isRequired="{ row, $rowIndex }">
              <ElCheckbox
                v-if="row.parentId !== null"
                v-model="row.isRequire"
              />
            </template>

            <template #fieldType="{ row, $rowIndex }">
              {{ row.fieldType }}
            </template>
            <template #enumValue="{ row, $rowIndex }">
              <div v-if="row.parentId !== null">
                <ElInput
                  v-if="row.fieldType == 'TEXT'"
                  v-model="row.value"
                  style="width: 100%"
                />
                <ElInputNumber
                  v-if="row.fieldType == 'NUMBER'"
                  v-model="row.value"
                  style="width: 100%"
                />
                <ElInputNumber
                  v-if="row.fieldType == 'PERCENT'"
                  style="width: 100%"
                  v-model="row.value"
                  :precision="2"
                  :step="0.1"
                  :max="10"
                />
                <ElSelect
                  v-if="row.fieldType == 'ENUM'"
                  v-model="row.value"
                  style="width: 100%"
                >
                  <ElOption label="测试1" value="1" />
                  <ElOption label="测试2" value="2" />
                  <ElOption label="测试3" value="3" />
                </ElSelect>
                <ElDatePicker
                  v-if="row.fieldType == 'DATE'"
                  style="width: 100%"
                  v-model="row.value"
                  type="date"
                />
                <ElButton type="primary" size="small">插入</ElButton>
              </div>
            </template>
          </VxeGrid>
        </div>

        <div class="mandatory-item-container mb-2 h-[220px] w-full">
          <div class="btn-group mb-2">
            <ElButton type="primary" @click="setBandItem" size="small">
              设置强制条款
            </ElButton>
          </div>

          <div class="mandatory-table h-[200px] overflow-y-scroll">
            <ElTable
              :data="mandatoryTerm"
              border
              size="small"
              style="width: 100%"
            >
              <ElTableColumn prop="name" label="名称" width="180">
                <template #default="scope">
                  <div class="flex items-center gap-4">
                    <div>强制条款 {{ scope.$index + 1 }}</div>
                    <ElButton
                      type="danger"
                      size="small"
                      :icon="Delete"
                      @click="deleteMandatory(scope.row)"
                    >
                      删除
                    </ElButton>
                  </div>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="content" label="内容">
                <template #default="scope">
                  <ElPopover
                    effect="light"
                    trigger="hover"
                    placement="top-end"
                    width="240px"
                  >
                    <template #default>
                      <div>{{ scope.row.content }}</div>
                    </template>
                    <template #reference>
                      <div class="truncate">{{ scope.row.content }}</div>
                    </template>
                  </ElPopover>
                </template>
              </ElTableColumn>
            </ElTable>
          </div>
        </div>
      </div>
    </div>
  </ElDrawer>
</template>

<script lang="ts" setup>
import { computed, provide, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { CirclePlus, Delete, Search } from '@element-plus/icons-vue';
import {
  ElButton,
  ElCheckbox,
  ElDatePicker,
  ElDrawer,
  ElInput,
  ElInputNumber,
  ElOption,
  ElPopover,
  ElSelect,
  ElTable,
  ElTableColumn,
  ElTooltip,
} from 'element-plus';

import './style.css';

defineOptions({
  name: 'ContractWord',
});

const props = withDefaults(
  defineProps<{
    dcoxFile: File | undefined;
    fieldRule: any;
    formData: any;
    mandatoryTerm: any;
    visible: boolean;
  }>(),
  {
    formData: () => ({}),
    mandatoryTerm: () => [],
    fieldRule: () => ({}),
    dcoxFile: undefined,
    visible: false,
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'submit', payload: SubmitPayload): void;
  (e: 'deleteMandatory', payload: any): void;
  (e: 'setFiled', payload: any): void;
}>();
interface SubmitPayload {
  name: string;
  remark: string;
  file: File;
}
const wpsRef = ref(); // wpsDom
const drawerVisible = ref(false); // 弹窗是否展示
const isFullScreen = ref(false); // 是否全屏
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '70%';
});
const tableRef = ref(); // 表格Dom
const fieldRule = ref([]); // 字段规则
const mandatoryTerm = ref([]); // 强条数据
const contractTemplateName = ref(''); // 合同范本名称
const contractTemplateDesc = ref(''); // 合同范本说明
const formData = ref({
  name: '',
  remark: '',
});
const dcoxFile = ref<File | undefined>(); // 传入文件
// 强制条款数据
const columns = [
  {
    field: 'name',
    title: '字段名称',
    minWidth: '200',
    slots: {
      default: 'name',
    },
    treeNode: true,
  },

  {
    field: 'isRequired',
    title: '必填',
    width: '80',
    slots: {
      default: 'isRequired',
    },
  },
  {
    field: 'fieldType',
    title: '字段类型',
    width: '80',
    slots: {
      default: 'fieldType',
    },
  },
  {
    field: 'enumValue',
    title: '枚举值',
    width: '180',
    slots: {
      default: 'enumValue',
    },
  },
];
const tableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  rowClassName: ({ row }: { row: { parentId: null | string } }) => {
    return row.parentId ? '' : 'bg-gray-100';
  },
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  checkboxConfig: {
    labelField: 'name',
    highlight: true,
  },
  columns,
  data: [],
});
watch(
  () => props.formData,
  (nval) => {
    if (nval) {
      formData.value = { ...nval };
      contractTemplateName.value = formData.value.name;
      contractTemplateDesc.value = formData.value.remark;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.fieldRule,
  (nval) => {
    if (nval) {
      fieldRule.value = nval;
      tableOptions.data = fieldRule.value;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.mandatoryTerm,
  (nval) => {
    if (nval) {
      mandatoryTerm.value = nval;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.dcoxFile,
  (nval) => {
    if (nval) {
      dcoxFile.value = nval;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
async function insureSubmit() {
  // 获取文件并上传
  const res: any = '';
  const params = {
    name: contractTemplateName.value, // 范本名称
    remark: contractTemplateDesc.value, // 范本说明
    file: res,
  };
  emit('submit', params);
}
function handleClose() {
  drawerVisible.value = false;
  emit('update:visible', false);
}
function handleOpen() {
  initEditor(dcoxFile.value);
}
function delay(time: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, time));
}
type fun = (payload: any) => void;
const listeners: fun[] = [];

provide('registerRangeStyleChange', (cb: fun) => {
  listeners.push(cb);
});
const expand = (type: string) => {
  if (type === 'close') {
    tableRef.value.clearTreeExpand();
  } else {
    tableRef.value.setAllTreeExpand(true);
  }
};
const setBandItem = () => {};
const searchValue = ref('');
function deleteMandatory(row: any) {
  emit('deleteMandatory', row);
}
function setFiledClick(row: any) {
  emit('setFiled', row);
}
// declare const WebOfficeSDK: any;
const accessStore = useAccessStore();
const refreshToken = () => {
  const token = accessStore.refreshToken;
  return new Promise((resolve, reject) => {
    // 异步操作
    setTimeout(() => {
      const success = true; // 模拟成功或失败
      if (success) {
        resolve({
          token,
          timeout: 10 * 60 * 1000,
        });
      } else {
        reject(new Error('刷新失败'));
      }
    }, 1000);
  });
};
const initEditor = async (file: File) => {
  const token = accessStore.accessToken;
  await delay(300);
  const mount = wpsRef.value;
};
</script>

<style lang="scss">
.el-drawer__header {
  margin-bottom: 0;
  padding: 0 16px;
}

.el-drawer__body {
  padding: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>
