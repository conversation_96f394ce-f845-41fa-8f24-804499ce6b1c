<template>
  <div class="footer" editor-component="footer">
    <div>
      <div class="catalog-mode" title="目录">
        <i></i>
      </div>
      <div class="page-mode">
        <i title="页面模式(分页、连页)"></i>
        <div class="options">
          <ul>
            <li data-page-mode="paging" class="active">分页</li>
            <li data-page-mode="continuity">连页</li>
          </ul>
        </div>
      </div>
      <span>可见页码：<span class="page-no-list">1</span></span>
      <span
        >页面：<span class="page-no">1</span>/<span class="page-size"
          >2</span
        ></span
      >
      <span>字数：<span class="word-count">571</span></span>
    </div>
    <div class="editor-mode" title="编辑模式(编辑、清洁、只读、表单、设计)">
      编辑模式
    </div>
    <div>
      <div class="page-scale-minus" title="缩小(Ctrl+-)">
        <i></i>
      </div>
      <span class="page-scale-percentage" title="显示比例(点击可复原Ctrl+0)"
        >100%</span
      >
      <div class="page-scale-add" title="放大(Ctrl+=)">
        <i></i>
      </div>
      <div class="paper-size">
        <i title="纸张类型"></i>
        <div class="options">
          <ul>
            <li data-paper-size="794*1123" class="active">A4</li>
            <li data-paper-size="1593*2251">A2</li>
            <li data-paper-size="1125*1593">A3</li>
            <li data-paper-size="565*796">A5</li>
            <li data-paper-size="412*488">5号信封</li>
            <li data-paper-size="450*866">6号信封</li>
            <li data-paper-size="609*862">7号信封</li>
            <li data-paper-size="862*1221">9号信封</li>
            <li data-paper-size="813*1266">法律用纸</li>
            <li data-paper-size="813*1054">信纸</li>
          </ul>
        </div>
      </div>
      <div class="paper-direction">
        <i title="纸张方向"></i>
        <div class="options">
          <ul>
            <li data-paper-direction="vertical" class="active">纵向</li>
            <li data-paper-direction="horizontal">横向</li>
          </ul>
        </div>
      </div>
      <div class="paper-margin" title="页边距">
        <i></i>
      </div>
      <div class="fullscreen" title="全屏显示">
        <i></i>
      </div>
      <div class="editor-option" title="编辑器设置">
        <i></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PageMode, PaperDirection } from '@hufe921/canvas-editor';
import Editor, { EditorMode } from '@hufe921/canvas-editor';
import { Dialog } from '../dialog/Dialog';
import { debounce, nextTick } from '../../utils';
import { inject } from 'vue';
const registerRangeStyleChange = inject('registerRangeStyleChange');
function init(
  instance: Editor,
  {
    isCatalogShow,
    updateCatalog,
  }: { isCatalogShow: boolean; updateCatalog: Function },
) {
  // 6. 目录显隐 | 页面模式 | 纸张缩放 | 纸张大小 | 纸张方向 | 页边距 | 全屏 | 设置
  const editorOptionDom =
    document.querySelector<HTMLDivElement>('.editor-option')!;
  editorOptionDom.onclick = function () {
    const options = instance.command.getOptions();
    new Dialog({
      title: '编辑器配置',
      data: [
        {
          type: 'textarea',
          name: 'option',
          width: 350,
          height: 300,
          required: true,
          value: JSON.stringify(options, null, 2),
          placeholder: '请输入编辑器配置',
        },
      ],
      onConfirm: (payload) => {
        const newOptionValue = payload.find((p) => p.name === 'option')?.value;
        if (!newOptionValue) return;
        const newOption = JSON.parse(newOptionValue);
        instance.command.executeUpdateOptions(newOption);
      },
    });
  };

  const pageModeDom = document.querySelector<HTMLDivElement>('.page-mode')!;
  const pageModeOptionsDom =
    pageModeDom.querySelector<HTMLDivElement>('.options')!;
  pageModeDom.onclick = function () {
    pageModeOptionsDom.classList.toggle('visible');
  };
  pageModeOptionsDom.onclick = function (evt) {
    const li = evt.target as HTMLLIElement;
    instance.command.executePageMode(<PageMode>li.dataset.pageMode!);
  };

  document.querySelector<HTMLDivElement>('.page-scale-percentage')!.onclick =
    function () {
      console.log('page-scale-recovery');
      instance.command.executePageScaleRecovery();
    };

  document.querySelector<HTMLDivElement>('.page-scale-minus')!.onclick =
    function () {
      console.log('page-scale-minus');
      instance.command.executePageScaleMinus();
    };

  document.querySelector<HTMLDivElement>('.page-scale-add')!.onclick =
    function () {
      console.log('page-scale-add');
      instance.command.executePageScaleAdd();
    };

  // 纸张大小
  const paperSizeDom = document.querySelector<HTMLDivElement>('.paper-size')!;
  const paperSizeDomOptionsDom =
    paperSizeDom.querySelector<HTMLDivElement>('.options')!;
  paperSizeDom.onclick = function () {
    paperSizeDomOptionsDom.classList.toggle('visible');
  };
  paperSizeDomOptionsDom.onclick = function (evt) {
    const li = evt.target as HTMLLIElement;
    const paperType = li.dataset.paperSize!;
    const [width, height] = paperType.split('*').map(Number);
    instance.command.executePaperSize(width, height);
    // 纸张状态回显
    paperSizeDomOptionsDom
      .querySelectorAll('li')
      .forEach((child) => child.classList.remove('active'));
    li.classList.add('active');
  };

  // 纸张方向
  const paperDirectionDom =
    document.querySelector<HTMLDivElement>('.paper-direction')!;
  const paperDirectionDomOptionsDom =
    paperDirectionDom.querySelector<HTMLDivElement>('.options')!;
  paperDirectionDom.onclick = function () {
    paperDirectionDomOptionsDom.classList.toggle('visible');
  };
  paperDirectionDomOptionsDom.onclick = function (evt) {
    const li = evt.target as HTMLLIElement;
    const paperDirection = li.dataset.paperDirection!;
    instance.command.executePaperDirection(<PaperDirection>paperDirection);
    // 纸张方向状态回显
    paperDirectionDomOptionsDom
      .querySelectorAll('li')
      .forEach((child) => child.classList.remove('active'));
    li.classList.add('active');
  };

  // 页面边距
  const paperMarginDom =
    document.querySelector<HTMLDivElement>('.paper-margin')!;
  paperMarginDom.onclick = function () {
    const [topMargin, rightMargin, bottomMargin, leftMargin] =
      instance.command.getPaperMargin();
    new Dialog({
      title: '页边距',
      data: [
        {
          type: 'text',
          label: '上边距',
          name: 'top',
          required: true,
          value: `${topMargin}`,
          placeholder: '请输入上边距',
        },
        {
          type: 'text',
          label: '下边距',
          name: 'bottom',
          required: true,
          value: `${bottomMargin}`,
          placeholder: '请输入下边距',
        },
        {
          type: 'text',
          label: '左边距',
          name: 'left',
          required: true,
          value: `${leftMargin}`,
          placeholder: '请输入左边距',
        },
        {
          type: 'text',
          label: '右边距',
          name: 'right',
          required: true,
          value: `${rightMargin}`,
          placeholder: '请输入右边距',
        },
      ],
      onConfirm: (payload) => {
        const top = payload.find((p) => p.name === 'top')?.value;
        if (!top) return;
        const bottom = payload.find((p) => p.name === 'bottom')?.value;
        if (!bottom) return;
        const left = payload.find((p) => p.name === 'left')?.value;
        if (!left) return;
        const right = payload.find((p) => p.name === 'right')?.value;
        if (!right) return;
        instance.command.executeSetPaperMargin([
          Number(top),
          Number(right),
          Number(bottom),
          Number(left),
        ]);
      },
    });
  };

  // 全屏
  const fullscreenDom = document.querySelector<HTMLDivElement>('.fullscreen')!;
  fullscreenDom.onclick = toggleFullscreen;
  window.addEventListener('keydown', (evt) => {
    if (evt.key === 'F11') {
      toggleFullscreen();
      evt.preventDefault();
    }
  });
  document.addEventListener('fullscreenchange', () => {
    fullscreenDom.classList.toggle('exist');
  });
  function toggleFullscreen() {
    console.log('fullscreen');
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  // 7. 编辑器使用模式
  let modeIndex = 0;
  const modeList = [
    {
      mode: EditorMode.EDIT,
      name: '编辑模式',
    },
    {
      mode: EditorMode.CLEAN,
      name: '清洁模式',
    },
    {
      mode: EditorMode.READONLY,
      name: '只读模式',
    },
    {
      mode: EditorMode.FORM,
      name: '表单模式',
    },
    {
      mode: EditorMode.PRINT,
      name: '打印模式',
    },
    {
      mode: EditorMode.DESIGN,
      name: '设计模式',
    },
  ];
  const modeElement = document.querySelector<HTMLDivElement>('.editor-mode')!;
  modeElement.onclick = function () {
    // 模式选择循环
    modeIndex === modeList.length - 1 ? (modeIndex = 0) : modeIndex++;
    // 设置模式
    const { name, mode } = modeList[modeIndex];
    modeElement.innerText = name;
    instance.command.executeMode(mode);
    // 设置菜单栏权限视觉反馈
    const isReadonly = mode === EditorMode.READONLY;
    const enableMenuList = ['search', 'print'];
    document
      .querySelectorAll<HTMLDivElement>('.menu-item>div')
      .forEach((dom) => {
        const menu = dom.dataset.menu;
        isReadonly && (!menu || !enableMenuList.includes(menu))
          ? dom.classList.add('disable')
          : dom.classList.remove('disable');
      });
  };

  // 内部事件监听
  // registerRangeStyleChange((payload) => {});

  instance.listener.visiblePageNoListChange = function (payload) {
    const text = payload.map((i) => i + 1).join('、');
    document.querySelector<HTMLSpanElement>('.page-no-list')!.innerText = text;
  };
  instance.listener.pageSizeChange = function (payload) {
    document.querySelector<HTMLSpanElement>('.page-size')!.innerText =
      `${payload}`;
  };
  instance.listener.intersectionPageNoChange = function (payload) {
    document.querySelector<HTMLSpanElement>('.page-no')!.innerText = `${
      payload + 1
    }`;
  };
  instance.listener.pageScaleChange = function (payload) {
    document.querySelector<HTMLSpanElement>(
      '.page-scale-percentage',
    )!.innerText = `${Math.floor(payload * 10 * 10)}%`;
  };
  const handleContentChange = async function () {
    // 字数
    const wordCount = await instance.command.getWordCount();
    document.querySelector<HTMLSpanElement>('.word-count')!.innerText = `${
      wordCount || 0
    }`;
    // 目录
    if (isCatalogShow) {
      nextTick(() => {
        updateCatalog();
      });
    }
    // 批注
    // nextTick(() => {
    //   updateComment();
    // });
  };
  instance.listener.contentChange = debounce(handleContentChange, 200);
  handleContentChange();

  instance.listener.pageModeChange = function (payload) {
    const activeMode = pageModeOptionsDom.querySelector<HTMLLIElement>(
      `[data-page-mode='${payload}']`,
    )!;
    pageModeOptionsDom
      .querySelectorAll('li')
      .forEach((li) => li.classList.remove('active'));
    activeMode.classList.add('active');
  };
}

defineExpose({
  init,
});
</script>

<style scoped>
@import './footer.css';
</style>
