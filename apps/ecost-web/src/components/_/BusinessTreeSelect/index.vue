<template>
  <ElTreeSelect
    v-bind="$attrs"
    :data="dataList"
    v-model="selectedValue"
    show-checkbox
    check-strictly
    default-expand-all
    node-key="id"
    :props="{
      disabled: (data: any) => !data.isLeaf,
      label: 'name',
      children: 'children',
    }"
    filterable
    :teleported="false"
    :filter-node-method="filterNodeMethod"
    @change="handleChange"
    @check-change="handleCheckChange"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { ElTreeSelect } from 'element-plus';

interface TreeDataItem {
  value: number | string;
  label: string;
  children?: TreeDataItem[];
  [key: string]: any;
}

const props = defineProps<{
  data?: TreeDataItem[];
  modelValue?: number | number[] | string | string[];
}>();

const emit = defineEmits<{
  (
    e: 'update:modelValue',
    value: number | number[] | string | string[] | undefined,
  ): void;
  (e: 'change', value: number | number[] | string | string[] | undefined): void;
  (
    e: 'checkChange',
    payload: { checked: boolean; data: TreeDataItem; indeterminate: boolean },
  ): void;
}>();

const dataList = computed(() => props.data || []);

const selectedValue = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val);
  },
});

const handleChange = (
  value: number | number[] | string | string[] | undefined,
) => {
  emit('change', value);
};

const handleCheckChange = (
  data: TreeDataItem,
  checked: boolean,
  indeterminate: boolean,
) => {
  emit('checkChange', {
    data,
    checked,
    indeterminate,
  });
};

const filterNodeMethod = (value: string, data: TreeDataItem) =>
  data.name.includes(value);
</script>

<style></style>
