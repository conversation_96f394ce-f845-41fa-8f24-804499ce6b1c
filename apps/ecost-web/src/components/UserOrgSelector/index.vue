<!-- 只于自身相关的组织树组件 -->
<template>
  <div class="org-selector">
    <ElTreeSelect
      class="w-180px"
      v-model="xOrgId"
      :data="data"
      :props="{
        value: 'id',
        label: 'name',
        disabled: (v: TreeData) => !v.isDirect,
        children: 'children',
      }"
      default-expand-all
      filterable
      :check-strictly="true"
      :filter-node-method="filterNode"
      @change="handleChangeOrganization"
    />
  </div>
</template>

<script lang="ts" setup>
import type { TreeData } from './index.interface';

import { onBeforeMount, ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH } from '@vben/constants';
import { useTabs } from '@vben/hooks';

import { ElTreeSelect } from 'element-plus';

import { getUserOrganizationTree } from '#/api/systemManagementApi/organizationApis';

defineOptions({
  name: 'UserOrgSelector',
});

const props = withDefaults(
  defineProps<{
    orgId: string;
  }>(),
  {
    orgId: '',
  },
);

const { closeAllTabs, refreshTab } = useTabs();
const router = useRouter();

interface Tree {
  [key: string]: any;
}

const xOrgId = ref(props.orgId);
const data = ref([]);

onBeforeMount(async () => {
  getUserOrganizationTree({}).then((res) => {
    data.value = res;

    if (!xOrgId.value) {
      const org = findFirstDirectOrg(res);
      xOrgId.value = org.id;
      sessionStorage.setItem('x-org-id', org.id);
      sessionStorage.setItem('x-org-type', org.type);
    }
  });
});

function findFirstDirectOrg(orgData: any) {
  for (const item of orgData) {
    if (item.isDirect) {
      return item;
    }
    if (Array.isArray(item.children)) {
      const result: any = findFirstDirectOrg(item.children);
      if (result) {
        return result;
      }
    }
  }

  return null;
}

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

// 递归查找节点
function findNode(id: string, data: TreeData[]): null | TreeData {
  for (const item of data) {
    if (item.id === id) {
      return item;
    }
    if (item.children && item.children.length > 0) {
      const result = findNode(id, item.children);
      if (result) {
        return result;
      }
    }
  }

  return null;
}

// 组织变更
async function handleChangeOrganization() {
  sessionStorage.setItem('x-org-id', xOrgId.value);

  const oldOrgType = sessionStorage.getItem('x-org-type') || '';
  const newOrgType = findNode(xOrgId.value, data.value)?.type || '';
  sessionStorage.setItem('x-org-type', newOrgType);

  if (
    oldOrgType === newOrgType ||
    // 租户和公司都认为是相同的类型
    (['COMPANY', 'TENANT'].includes(oldOrgType) &&
      ['COMPANY', 'TENANT'].includes(newOrgType))
  ) {
    await refreshTab();
  } else {
    await closeAllTabs();
    await router.push(DEFAULT_HOME_PATH);
    window.location.reload();
  }
}
</script>
<style scoped></style>
