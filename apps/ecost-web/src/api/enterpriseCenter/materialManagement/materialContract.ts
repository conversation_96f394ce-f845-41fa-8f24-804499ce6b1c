import { ecostServiceRequestClient } from '#/api/request';

/**
 * 合同编制 -- 前置查询（甲方公司）
 *
 */
interface PartyACompanyType {
  name?: string; // 公司名称
}
export function getPartyACompanyList(params: PartyACompanyType) {
  return ecostServiceRequestClient.get('/contract-compilation/partyA-company', {
    params,
  });
}

/**
 * 合同编制 -- 前置查询（甲方公司）
 *
 */
interface PartyBCompanyType {
  name?: string; // 公司名称
}
export function getPartyBCompanyList(params: PartyBCompanyType) {
  return ecostServiceRequestClient.get('/contract-compilation/partyB-company', {
    params,
  });
}

/**
 * 合同编制 -- 前置查询（合同范本）查询）
 *
 */
interface getContractTemplateListType {
  name?: string; // 公司名称
}
export function getContractTemplateList(params: getContractTemplateListType) {
  return ecostServiceRequestClient.get(
    '/contract-compilation/contract-template',
    {
      params,
    },
  );
}

/**
 * 合同编制 -- 查询列表
 *
 */
export function getContractCompilationList(params: any) {
  return ecostServiceRequestClient.get('/contract-compilation', {
    params,
  });
}

/**
 * 合同编制 -- 新增合同
 *
 */
export interface addContractCompilationType {
  parentId?: null | string;
  contractTemplateId: string; // 合同范本id
  name: string; // 合同名称
  code: string; // 合同编号
  partyA?: string; // 甲方id
  partyB?: string; // 乙方id
  partyBType?: string; // 乙方类型
  proposedStatus?: string; // 拟定状态
}
export function addContractCompilation(data: addContractCompilationType) {
  return ecostServiceRequestClient.post('/contract-compilation', data);
}

/**
 * 合同编制 -- 修改合同
 *
 */
export function editContractCompilation(
  id: string,
  data: addContractCompilationType,
) {
  return ecostServiceRequestClient.patch(`/contract-compilation/${id}`, data);
}

/**
 * 合同编制 -- 删除合同
 *
 */
export function delContractCompilation(id: string) {
  return ecostServiceRequestClient.delete(`/contract-compilation/${id}`);
}

/**
 * 合同编制 -- 获取字段规则列表
 *
 */
export function getContractFieldList(id: string) {
  return ecostServiceRequestClient.get(
    `/contract-compilation/rule-field/${id}`,
  );
}

/**
 * 合同编制 -- 新增编辑字段规则列表
 *
 */

export interface addOrEditContractFieldType {
  ruleList: string; // 合同范本id
}
export function addOrEditContractField(
  id: string,
  data: addOrEditContractFieldType,
) {
  return ecostServiceRequestClient.patch(
    `/contract-compilation/rule-field/${id}`,
    data,
  );
}

/**
 * 合同编制 --合同提交状态更改
 *
 */
export interface changeSubmitStatusType {
  submitStatus: string; // 合同范本id
}
export function changeSubmitStatus(id: string, data: changeSubmitStatusType) {
  return ecostServiceRequestClient.patch(
    `/contract-compilation/submitStatus/${id}`,
    data,
  );
}

/**
 * 合同编制 -- 合同审核状态更改
 *
 */
export interface changeReviewStatusType {
  auditStatus: string; // 合同范本id
}
export function changeReviewStatus(id: string, data: changeReviewStatusType) {
  return ecostServiceRequestClient.patch(
    `/contract-compilation/auditStatus/${id}`,
    data,
  );
}

/**
 * 合同编制 -- 查询可以选择的材料分类
 *
 */
export interface GetMaterialCategoryListType {
  name?: string;
}
export function getMaterialCategoryList(
  materialContractId: string,
  params: GetMaterialCategoryListType,
) {
  return ecostServiceRequestClient.get(
    `/contract-material-detail/material-category/${materialContractId}`,
    {
      params,
    },
  );
}

/**
 * 合同编制 -- 查询可以选择的材料明细
 *
 */

interface MaterialDetailType {
  materialContractId: string; // 合同ID
  materialDictionaryCategoryId: string; // 分类ID
}
export function getMaterialDetailList(params: MaterialDetailType) {
  return ecostServiceRequestClient.get(
    `/contract-material-detail/material-detail`,
    {
      params,
    },
  );
}

/**
 * 合同编制 -- 新增合同材料
 *
 */

interface addContractMaterialDetailType {
  list: any; // 合同ID
  materialContractId: string; // 分类ID
  contractTemplateType: string; // 分类ID
}
export function addContractMaterialDetail(data: addContractMaterialDetailType) {
  return ecostServiceRequestClient.post(`/contract-material-detail`, data);
}

/**
 * 合同编制 -- 编辑合同材料
 *
 */

interface editContractMaterialDetailType {
  // 周转材料 物资采购合同 商品混凝土
  contractTemplateType: string; // 合同类型
  unit: string; // 单位
  changeQuantity: number; // 数量
  changePriceExcludingTax: number; // 不含税单价
  changePriceIncludingTax: number; // 含税单价
  changeAddedTaxAmount: number; // 增值税额

  remark: string; // 合同说明
  changeTotalPriceExcludingTax: number; // 不含税总价
  changeTotalValueAddedTaxAmount: number; // 增值税总额
  changeTotalPriceIncludingTax: number; // 含税总价
  // 只有物资采购合同存在
  qualityStandard?: string; // 材质、性能参数等（或执行的技术质量标准 (物资采购合同)
  brand?: string; // 源合同/品牌或厂家 (物资采购合同)

  // 只有补充协议存在
  alterType?: string; // 变更类型
  priceCalculate?: string; // 变更价格增减
  quantityCalculate?: number; // 变更数量增减
  changePercentage?: string; // 增减百分比

  changeCalculateTotalPriceExcludingTax?: string; // 变更增减不含税总价
  changeCalculateTotalValueAddedTaxAmount?: string; // 变更增减增值税总额
  changeCalculateTotalPriceIncludingTax?: string; // 变更增减含税总价
  // 只有周转材料存在
  changeProvisionalDays?: number; // 变更后天数
}
export function editContractMaterialDetail(
  id: string,
  data: editContractMaterialDetailType,
) {
  return ecostServiceRequestClient.patch(
    `/contract-material-detail/${id}`,
    data,
  );
}

/**
 * 合同编制 -- 合同材料上移下移
 *
 */
interface MoveContractMaterialType {
  id: string;
  moveType: 'down' | 'up';
  materialContractId: string;
  contractTemplateType: string;
}
export function moveContractMaterial(data: MoveContractMaterialType) {
  return ecostServiceRequestClient.post(`/contract-material-detail/move`, data);
}

/**
 * 合同编制 -- 删除已选择材料
 *
 */
export function delContractMaterialDetail(id: string, contractId: string) {
  return ecostServiceRequestClient.delete(
    `/contract-material-detail/detail/${id}/${contractId}`,
  );
}

/**
 * 合同编制 -- 获取合同下选择的合同材料
 *
 */

export interface getContractMaterialDetailType {
  contractId: string;
  contractTemplateType: string; // 分类ID
}
export function getContractMaterialDetail(
  contractId: string,
  params: getContractMaterialDetailType,
) {
  return ecostServiceRequestClient.get(
    `/contract-material-detail/${contractId}`,
    {
      params,
    },
  );
}

/**
 * 合同编制 -- 查询某明细的单位换算信息
 *
 */

export interface getContractMaterialUnitType {
  materialDetailId: string; // 已选明细id
  materialContractId: string; // 材料明细id
}
export function getContractMaterialUnit(params: getContractMaterialUnitType) {
  return ecostServiceRequestClient.get(`/unit-calculation`, {
    params,
  });
}

/**
 * 合同编制 -- 新增某明细的单位换算信息
 *
 */

export interface addContractMaterialUnitType {
  unit: string; // 已选明细id
  factor: number; // 材料明细id
  remark: string; // 材料明细id
  materialDetailId: string; // 材料明细id
}
export function addContractMaterialUnit(data: addContractMaterialUnitType) {
  return ecostServiceRequestClient.post(`/unit-calculation`, data);
}

/**
 * 合同编制 -- 编辑某明细的单位换算信息
 *
 */

export interface editContractMaterialUnitType {
  unit: string; // 单位
  factor: number; // 换算系数
  remark?: string; // 备注
}
export function editContractMaterialUnit(
  id: string,
  data: editContractMaterialUnitType,
) {
  return ecostServiceRequestClient.patch(`/unit-calculation/${id}`, data);
}

/**
 * 合同编制 -- 删除某明细的单位换算信息
 *
 */
export function delContractMaterialUnit(id: string) {
  return ecostServiceRequestClient.delete(`/unit-calculation/${id}`);
}

/**
 * 合同编制 -- 附加费
 *
 */

export function getSurchargeList(contractId: string) {
  return ecostServiceRequestClient.get(
    `/contract-concrete-surcharge/${contractId}`,
  );
}
/**
 * 合同编制 -- 修改附加费
 *
 */

export interface editSurchargeType {
  changeCalculatePrice: number;
  changePrice: number;
  remark?: string;
}
export function editSurcharge(id: string, data: editSurchargeType) {
  return ecostServiceRequestClient.patch(
    `/contract-concrete-surcharge/${id}`,
    data,
  );
}

/**
 * 合同编制 -- 新增合同附件
 *
 */
export interface addContractAccessoryType {
  materialContractId: string;
  fileName: string;
  fileExt: string;
  fileKey: string;
  fileSize: string;
  fileContentType: string;
  remark?: string;
}
export function addContractAccessory(data: addContractAccessoryType) {
  return ecostServiceRequestClient.post(`/material-contract-accessory`, data);
}

/**
 * 合同编制 -- 获取合同附件
 *
 */
export function getContractAccessoryList(contractId: string) {
  return ecostServiceRequestClient.get(
    `/material-contract-accessory/${contractId}`,
  );
}

/**
 * 合同编制 -- 删除合同附件
 *
 */
export function delContractAccessory(id: string) {
  return ecostServiceRequestClient.delete(`/material-contract-accessory/${id}`);
}

/**
 * 合同编制 -- 导出合同附件
 *
 */
export function exportContractCompilation() {
  return ecostServiceRequestClient.get(`/contract-compilation/export`, {
    responseType: 'blob',
  });
}

/**
 * 合同编制 -- 判断是否可以进入货物清单
 *
 */
interface checkContractCompilationFiledType {
  id: string; // id
  contractTemplateId: string; // 合同范本Id
}
export function checkContractCompilationFiled(
  params: checkContractCompilationFiledType,
) {
  return ecostServiceRequestClient.get('/contract-compilation/check-field', {
    params,
  });
}

/**
 * 合同编制 -- 刷新 合同编制中的 货物清单
 *
 */

export function refreshContractGoodList(id: string) {
  return ecostServiceRequestClient.patch(
    `/contract-compilation/calculatePrice/${id}`,
  );
}
