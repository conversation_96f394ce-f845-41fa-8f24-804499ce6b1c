import type {
  AuditStatusType,
  SubmitStatusType,
} from '#/types/materialManagement';

import { ecostServiceRequestClient } from '#/api/request';

export const MaterialReceiptStatus = {
  UN_RECEIVED: 'UN_RECEIVED',
  RECEIVED: 'RECEIVED',
  PARTIAL_RECEIVED: 'PARTIAL_RECEIVED',
} as const;
export type MaterialReceiptStatusEnum =
  (typeof MaterialReceiptStatus)[keyof typeof MaterialReceiptStatus];

export const MaterialType = {
  CONSUME_MATERIAL: 'CONSUME_MATERIAL',
  CONCRETE: 'CONCRETE',
  TURNOVERME_MATERIAL: 'TURNOVERME_MATERIAL',
  FIXEDASSETSL_CONSUMABLES: 'FIXEDASSETSL_CONSUMABLES',
} as const;
export type MaterialTypeEnmu = (typeof MaterialType)[keyof typeof MaterialType];

export const MaterialSearchType = {
  CONTRACT: 'CONTRACT',
  MATERIAL_DICT: 'MATERIAL_DICT',
} as const;
export type MaterialSearchTypeEnum =
  (typeof MaterialSearchType)[keyof typeof MaterialSearchType];

/**
 * 调拨单 -- 获取时间筛选列表
 *
 */
export function getTimeList() {
  return ecostServiceRequestClient.get(`/material-allocation-from/date-tree`);
}

/**
 * 调拨单 -- 获取单据列表
 *
 */
interface ReceiptListType {
  year?: number;
  month?: number;
  day?: number;
}
export function getReceiptList(params?: ReceiptListType) {
  return ecostServiceRequestClient.get(`/material-allocation-from`, {
    params,
  });
}

/**
 * 调拨单 -- 新增单据
 *
 */
export function addNewReceipt() {
  return ecostServiceRequestClient.post(`/material-allocation-from`);
}

/**
 * 调拨单 -- 删除单据
 *
 */
export function delReceipt(id: string) {
  return ecostServiceRequestClient.delete(`/material-allocation-from/${id}`);
}
/**
 * 调拨单 -- 编辑调拨单
 *
 */
interface EditMaterialReceingType {
  transferInProjectId: string;
  year: number;
  month: number;
  day: number;
}
export function editReceipt(id: string, data: EditMaterialReceingType) {
  return ecostServiceRequestClient.patch(
    `/material-allocation-from/${id}`,
    data,
  );
}

/**
 * 调拨单 -- 变更提交状态
 *
 */
interface editInspectionBillType {
  submitStatus: SubmitStatusType;
}
export function changeSubmitStatus(id: string, data: editInspectionBillType) {
  return ecostServiceRequestClient.patch(
    `/material-allocation-from/submit/${id}`,
    data,
  );
}
/**
 * 调拨单 -- 变更提交状态
 *
 */

export function changeAuditStatus(id: string, auditStatus: AuditStatusType) {
  const data = {
    auditStatus,
  };

  return ecostServiceRequestClient.patch(
    `/material-allocation-from/audit/${id}`,
    data,
  );
}

/* ----------------------------------------------明细的接口------------------------------------------------------------- */

/**
 * 调拨单 -- 查询可选的材料分类
 *
 */

export function getMaterialCategory() {
  return ecostServiceRequestClient.get(
    `/material-allocation-from-detail/choose/materialCategory`,
  );
}

/**
 * 调拨单 -- 查询可选的材料明细
 *
 */

export function getMaterialDetail(
  categoryId: string, // 分类id
  materialAllocationFromId: string, // 调拨单id
) {
  const params = {
    materialAllocationFromId,
    categoryId,
  };

  return ecostServiceRequestClient.get(
    `/material-allocation-from-detail/choose/materialDetails`,
    {
      params,
    },
  );
}

/**
 * 调拨单 -- 查询明细
 *
 */

export function getReceptDetailList(id: string) {
  return ecostServiceRequestClient.get(
    `/material-allocation-from-detail/${id}`,
  );
}

/**
 * 调拨单 -- 新增明细
 *
 */
interface addInspectionDetailType {
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
}
export function addInspectionDetail(
  materialAllocationFromId: string,
  data: addInspectionDetailType[],
) {
  // 额外处理一下数据
  const dataValue = {
    list: data,
    materialAllocationFromId,
  };

  return ecostServiceRequestClient.post(
    `/material-allocation-from-detail`,
    dataValue,
  );
}

/**
 * 调拨单 -- 编辑明细
 *
 */
interface EditReceptDetail {
  allocationQuantity: string;
  remark: string;
}
export function editReceptDetail(id: string, data: EditReceptDetail) {
  return ecostServiceRequestClient.patch(
    `/material-allocation-from-detail/${id}`,
    data,
  );
}

/**
 * 调拨单 -- 删除明细
 *
 */
export function delReceptDetail(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-allocation-from-detail/${id}`,
  );
}

/**
 * 调拨单 -- 明细上移下移
 *
 */
interface MoveType {
  fromId: string;
  toId: string;
}
export function moveReceptDetail(params: MoveType) {
  return ecostServiceRequestClient.post(
    `/material-allocation-from-detail/move`,
    {},
    {
      params,
    },
  );
}

/**
 * 调拨单 -- 附件上传
 *
 */
interface AddMaterialReceivingAttachmentType {
  materialAllocationFromId: string;
  fileName: string;
  fileKey: string;
  fileSize: number;
  fileExt: string;
  fileContentType: string;
}
export function addAttachment(data: AddMaterialReceivingAttachmentType) {
  return ecostServiceRequestClient.post(
    `/material-allocation-from-attachment`,
    data,
  );
}

/**
 * 调拨单 -- 获取附件列表
 *
 */

export function getAttachmentList(materialAllocationFromId: string) {
  return ecostServiceRequestClient.get(
    `/material-allocation-from-attachment/${materialAllocationFromId}`,
  );
}

/**
 * 调拨单 -- 删除附件
 *
 */
export function delAttachmen(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-allocation-from-attachment/${id}`,
  );
}
