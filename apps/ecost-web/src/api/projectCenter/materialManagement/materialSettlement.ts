import type {
  AuditStatusType,
  SubmitStatusType,
} from '#/types/materialManagement';

import { ecostServiceRequestClient } from '#/api/request';

/**
 * 物资结算单 -- 获取单据列表
 *
 */
export interface getMaterialSettlementListType {
  year?: number;
  month?: number;
  isCumulation?: boolean;
  materialCategoryId?: string;
  id?: string;
}
export function getMaterialSettlementList(
  params?: getMaterialSettlementListType,
) {
  return ecostServiceRequestClient.get(`/material-settlement`, {
    params,
  });
}

export function getMaterialSettlementPeriodList() {
  return ecostServiceRequestClient.get(`/material-settlement/periods`);
}

interface addMaterialSettlementType {
  settlementType: string;
}
export function addMaterialSettlement(data: addMaterialSettlementType) {
  return ecostServiceRequestClient.post('/material-settlement', data);
}

interface EditMaterialSettlementType {
  supplierId?: string;
  supplierName?: string;
  contractId?: string;
  contractName?: string;
  settlementDate?: string;
}
export function editMaterialSettlement(
  id: string,
  data: EditMaterialSettlementType,
) {
  return ecostServiceRequestClient.patch(`/material-settlement/${id}`, data);
}

/**
 * -- 变更提交状态
 *
 */
interface editInspectionBillType {
  submitStatus: SubmitStatusType;
}
export function changeSubmitStatus(id: string, data: editInspectionBillType) {
  return ecostServiceRequestClient.patch(
    `/material-settlement/submit/${id}`,
    data,
  );
}

/**
 * -- 变更审核状态
 *
 */

export function changeAuditStatus(id: string, auditStatus: AuditStatusType) {
  const data = {
    auditStatus,
  };

  return ecostServiceRequestClient.patch(
    `/material-settlement/audit/${id}`,
    data,
  );
}

/**
 * -- 删除单据
 *
 */
export function delInspectionBill(id: string) {
  return ecostServiceRequestClient.delete(`/material-settlement/${id}`);
}

export interface getMaterialSettlementDetailType {
  settlementId: string;
}
export function getSettlementDetails(params: getMaterialSettlementDetailType) {
  return ecostServiceRequestClient.get(`/material-settlement-detail`, {
    params,
  });
}

export function getSupplierAndContractList() {
  return ecostServiceRequestClient.get(
    `/material-settlement-detail/supplier-and-contract/list`,
  );
}

interface getReceivingReturnListType {
  supplierId: string;
  contractId: string;
  settlementId: string;
}
export function getReceivingReturnList(params: getReceivingReturnListType) {
  return ecostServiceRequestClient.get(
    `/material-settlement-detail/supplier-and-contract/receiving-returns`,
    {
      params,
    },
  );
}

interface getBillDetailType {
  settlementId: string;
  settlementDetailId: string;
}
export function getMaterialReceivingReturnDetails(params: getBillDetailType) {
  return ecostServiceRequestClient.get(
    `/material-settlement-detail/material/receiving-returns`,
    {
      params,
    },
  );
}

interface getReceivingReturnDetailType {
  settlementId: string;
  receivingReturnIds: string[];
}
export function getReceivingReturnDetail(data: getReceivingReturnDetailType) {
  return ecostServiceRequestClient.post(
    '/material-settlement-detail/supplier-and-contract/receiving-returns/summary',
    data,
  );
}

export function updateSettlementDetails(data: getReceivingReturnDetailType) {
  return ecostServiceRequestClient.patch(
    '/material-settlement-detail/supplier-and-contract/receiving-returns/summary',
    data,
  );
}

interface updateDetailType {
  id: string;
  settlementId: string;
  remark: string;
}
export function updateSettlementDetail(data: updateDetailType) {
  return ecostServiceRequestClient.patch(
    '/material-settlement-detail/update',
    data,
  );
}

export function delRecord(data: getReceivingReturnDetailType) {
  return ecostServiceRequestClient.patch(
    '/material-settlement-detail/supplier-and-contract/receiving-returns/delete-summary',
    data,
  );
}

interface moveInspectionDetailType {
  fromId: string;
  toId: string;
}
export function moveInspectionDetail(params: moveInspectionDetailType) {
  return ecostServiceRequestClient.post(
    `/material-settlement-detail/move`,
    {},
    {
      params,
    },
  );
}

/**
 * 附件上传
 *
 */
interface AddAttachmentType {
  settlementId: string;
  fileName: string;
  fileKey: string;
  fileSize: number;
  fileExt: string;
  fileContentType: string;
}
export function addMaterialReceivingAttachment(data: AddAttachmentType) {
  return ecostServiceRequestClient.post(
    `/material-settlement-attachment`,
    data,
  );
}

/**
 * 获取附件列表
 *
 */

export function getAttachmentList(settlementId: string) {
  return ecostServiceRequestClient.get(
    `/material-settlement-attachment/${settlementId}`,
  );
}

/**
 * 删除附件
 *
 */
export function delAttachment(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-settlement-attachment/${id}`,
  );
}
